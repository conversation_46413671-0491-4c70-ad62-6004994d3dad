<script setup>
import { onMounted, ref } from 'vue';

defineProps({
    modelValue: String,
});

defineEmits(['update:modelValue']);

const input = ref(null);

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});

defineExpose({ focus: () => input.value.focus() });
</script>

<template>
    <input
        ref="input"
        class="border-gray-300 focus:border-gorilla-primary focus:ring-4 focus:ring-gorilla-primary/10 rounded-xl transition-all duration-200 text-gorilla-primary-three"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
    >
</template>
