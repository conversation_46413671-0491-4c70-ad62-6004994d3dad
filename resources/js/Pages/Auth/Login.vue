<script setup>
import { Head, <PERSON>, useForm } from '@inertiajs/vue3';
import { ref } from 'vue';
import { Mail, Lock, Eye, EyeOff, ArrowRight, CheckCircle } from 'lucide-vue-next';

defineProps({
    canResetPassword: Boolean,
    status: String,
});

const form = useForm({
    email: '',
    password: '',
    remember: false,
});

const showPassword = ref(false);

const submit = () => {
    form.transform(data => ({
        ...data,
        remember: form.remember ? 'on' : '',
    })).post(route('login'), {
        onFinish: () => form.reset('password'),
    });
};
</script>

<template>
    <Head title="Welcome Back - OnRwanda Geo" />

    <div class="min-h-screen bg-gradient-to-br from-cta-background-two via-white to-cta-background-one flex items-center justify-center p-4">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=&quot;60&quot; height=&quot;60&quot; viewBox=&quot;0 0 60 60&quot; xmlns=&quot;http://www.w3.org/2000/svg&quot;%3E%3Cg fill=&quot;none&quot; fill-rule=&quot;evenodd&quot;%3E%3Cg fill=&quot;%23303017&quot; fill-opacity=&quot;0.1&quot;%3E%3Ccircle cx=&quot;30&quot; cy=&quot;30&quot; r=&quot;2&quot;/%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>

        <div class="relative w-full max-w-sm">
            <!-- Logo Section -->
            <div class="text-center mb-6">
                <Link :href="'/'" class="inline-block">
                    <div class="bg-white rounded-xl p-3 shadow-md border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                        <img src="/OnRwandaBlackNoIconLogo.png" alt="OnRwanda Geo" class="h-8 w-auto mx-auto"/>
                    </div>
                </Link>
                <h1 class="mt-4 text-2xl font-bold text-gorilla-primary-three">
                    Welcome Back
                </h1>
                <p class="mt-1 text-sm text-gray-600">
                    Sign in to continue
                </p>
            </div>

            <!-- Status Message -->
            <div v-if="status" class="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg flex items-center">
                <CheckCircle class="h-4 w-4 text-green-600 mr-2" />
                <span class="text-xs font-medium text-green-800">{{ status }}</span>
            </div>

            <!-- Login Card -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-100 p-6 backdrop-blur-sm">
                <form @submit.prevent="submit" class="space-y-4">
                    <!-- Email Field -->
                    <div class="space-y-1">
                        <label for="email" class="block text-xs font-semibold text-gorilla-primary-three">
                            Email
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Mail class="h-4 w-4 text-gray-400" />
                            </div>
                            <input
                                id="email"
                                v-model="form.email"
                                type="email"
                                class="w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:border-gorilla-primary focus:ring-2 focus:ring-gorilla-primary/20 transition-all duration-200 text-sm text-gorilla-primary-three placeholder-gray-400"
                                placeholder="<EMAIL>"
                                required
                                autofocus
                                autocomplete="username"
                            />
                        </div>
                        <div v-if="form.errors.email" class="text-red-600 text-xs mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-1"></span>
                            {{ form.errors.email }}
                        </div>
                    </div>

                    <!-- Password Field -->
                    <div class="space-y-1">
                        <label for="password" class="block text-xs font-semibold text-gorilla-primary-three">
                            Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <Lock class="h-4 w-4 text-gray-400" />
                            </div>
                            <input
                                id="password"
                                v-model="form.password"
                                :type="showPassword ? 'text' : 'password'"
                                class="w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:border-gorilla-primary focus:ring-2 focus:ring-gorilla-primary/20 transition-all duration-200 text-sm text-gorilla-primary-three placeholder-gray-400"
                                placeholder="••••••••"
                                required
                                autocomplete="current-password"
                            />
                            <button
                                type="button"
                                @click="showPassword = !showPassword"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gorilla-primary transition-colors"
                            >
                                <Eye v-if="!showPassword" class="h-4 w-4" />
                                <EyeOff v-else class="h-4 w-4" />
                            </button>
                        </div>
                        <div v-if="form.errors.password" class="text-red-600 text-xs mt-1 flex items-center">
                            <span class="w-1 h-1 bg-red-600 rounded-full mr-1"></span>
                            {{ form.errors.password }}
                        </div>
                    </div>

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between text-xs">
                        <label class="flex items-center cursor-pointer group">
                            <input
                                v-model="form.remember"
                                type="checkbox"
                                class="w-3 h-3 text-gorilla-primary border-gray-300 rounded focus:ring-gorilla-primary/20 focus:ring-1"
                            />
                            <span class="ml-2 text-gray-600 group-hover:text-gorilla-primary-three transition-colors">
                                Remember me
                            </span>
                        </label>

                        <Link
                            v-if="canResetPassword"
                            :href="route('password.request')"
                            class="text-gorilla-primary hover:text-gorilla-primary/80 font-medium transition-colors"
                        >
                            Forgot password?
                        </Link>
                    </div>

                    <!-- Login Button -->
                    <button
                        type="submit"
                        :disabled="form.processing"
                        class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center group disabled:opacity-50 disabled:cursor-not-allowed shadow-md hover:shadow-lg transform hover:scale-[1.01] active:scale-[0.99] text-sm"
                    >
                        <span v-if="!form.processing">Sign In</span>
                        <span v-else>Signing in...</span>
                        <ArrowRight v-if="!form.processing" class="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                        <div v-else class="ml-2 animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    </button>

                    <!-- Register Link -->
                    <div class="text-center pt-3 border-t border-gray-100">
                        <p class="text-gray-600 text-xs">
                            New to OnRwanda Geo?
                            <Link
                                :href="route('register')"
                                class="text-gorilla-primary hover:text-gorilla-primary/80 font-semibold ml-1 transition-colors"
                            >
                                Create account
                            </Link>
                        </p>
                    </div>
                </form>
            </div>

            <!-- Footer -->
            <div class="text-center mt-6">
                <p class="text-xs text-gray-400">
                    Explore Rwanda with precision
                </p>
            </div>
        </div>
    </div>
</template>
