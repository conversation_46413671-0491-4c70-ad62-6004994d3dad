<script setup>
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { ref } from 'vue';
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Hospital,
  Tractor,
  Truck,
  Building,
  Wrench,
  GraduationCap,
  Camera,
  Home,
  Smartphone,
  Zap,
  MapPin,
  Clock,
  Target,
  TrendingUp,
  Activity,
  CheckCircle,
  ArrowRight,
  Users,
  BarChart3,
  Navigation,
  Search,
  Database,
  Play,
  Pause,
  RotateCcw,
  ChevronRight,
  Quote,
  Star,
  Award,
  Shield,
  Globe,
  Layers,
  Eye,
  Download,
  Share2
} from 'lucide-vue-next';

// Reactive data
const activeSector = ref('health');
const activeWorkflow = ref(1);

// Sector data
const sectors = [
  {
    id: 'health',
    name: 'Health',
    icon: Hospital,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    borderColor: 'border-red-200',
    description: 'Optimizing Healthcare Delivery and Emergency Response',
    overview: 'In a healthcare system striving for Universal Health Coverage (UHC), every minute matters. Ngabo GIS enables Rwanda\'s health sector to optimize ambulance dispatch, manage medical supply chains, track disease outbreaks, and ensure equitable access to healthcare facilities.',
    challenges: [
      'Manual ambulance dispatch leads to delayed emergency response',
      'Difficulty tracking medical supply distribution to health facilities',
      'Limited visibility into disease outbreak patterns and spread',
      'Inefficient pharmaceutical inventory management',
      'Gaps in healthcare facility coverage, particularly in rural areas'
    ],
    impact: [
      'Average emergency response time: 45+ minutes in urban areas, 2+ hours rural',
      '30% of health facilities experience medication stockouts monthly',
      'Delayed outbreak response due to fragmented data',
      'Preventable deaths due to dispatch inefficiencies'
    ],
    metrics: [
      { label: 'Emergency Response Improvement', value: '42%', description: 'reduction in average ambulance dispatch time' },
      { label: 'Urban Response Time', value: '8 min', description: 'average response time (vs. 45 minutes)' },
      { label: 'Rural Response Time', value: '25 min', description: 'average response time (vs. 2+ hours)' },
      { label: 'Location Accuracy', value: '99.7%', description: 'for incident geocoding' },
      { label: 'Supply Chain Efficiency', value: '35%', description: 'reduction in pharmaceutical distribution costs' },
      { label: 'Stockout Reduction', value: '28%', description: 'decrease in medication stockouts' }
    ],
    workflows: [
      {
        id: 1,
        title: 'Emergency Ambulance Dispatch',
        scenario: 'Cardiac emergency reported in Kimironko, Kigali',
        steps: [
          {
            title: 'INCIDENT LOGGED',
            description: 'Emergency call received: "Cardiac emergency, Kimironko sector"',
            details: 'Dispatcher enters location into Ngabo GIS',
            time: '0 seconds'
          },
          {
            title: 'INDEXING ENGINE PROCESSING',
            description: 'Geocodes "Kimironko sector" to precise coordinates',
            details: 'Pattern recognition identifies sector-level administrative area, searches for nearest ambulances within 10km radius',
            time: '<50ms'
          },
          {
            title: 'DATA ENGINE ENRICHMENT',
            description: 'Retrieves ambulance status and equipment capabilities',
            details: 'Filters: 2 ambulances available, 1 ALS-equipped, ranks by distance and equipment match',
            time: '200ms'
          },
          {
            title: 'TRAVEL ENGINE OPTIMIZATION',
            description: 'Computes optimal route and provides navigation',
            details: 'Route: Ambulance (Remera) → Incident (Kimironko) = 3.2km, 8-minute ETA. Identifies nearest hospital: King Faisal (2.1km from incident)',
            time: '1.2 seconds'
          },
          {
            title: 'DISPATCH & TRACKING',
            description: 'Real-time coordination and monitoring',
            details: 'Ambulance dispatched with route, GPS tracking via WebSocket, hospital notified with ETA',
            time: 'Ongoing'
          }
        ],
        outcome: {
          title: 'Life-Saving Results',
          metrics: [
            'Total dispatch decision time: <2 seconds',
            'Ambulance arrival: 8 minutes (vs. 45-minute average)',
            'Patient transported to facility: 14 minutes total',
            'Life saved through optimal response time'
          ]
        }
      },
      {
        id: 2,
        title: 'Pharmaceutical Distribution Optimization',
        scenario: 'Distribute insulin to health facilities in Muhanga District',
        steps: [
          {
            title: 'INDEXING ENGINE SEARCH',
            description: 'Query: "Find health facilities needing insulin <10,000 RWF in Muhanga"',
            details: 'Pattern recognition identifies Muhanga District, searches health facility database',
            time: '45ms'
          },
          {
            title: 'DATA ENGINE FILTERING',
            description: 'Integrates with inventory management system',
            details: 'Filters by stock level, budget constraint, storage capability. Results: 7 facilities need restocking',
            time: '300ms'
          },
          {
            title: 'TRAVEL ENGINE TSP',
            description: 'Vehicle Routing Problem optimization',
            details: 'Depot: Central Medical Stores. 7 waypoints. Constraints: 200L capacity, 8AM-4PM windows, <2hrs cold chain',
            time: '2.8 seconds'
          }
        ],
        outcome: {
          title: 'Distribution Excellence',
          metrics: [
            'Route efficiency: 45% improvement vs. manual planning',
            'Fuel savings: 30% (47km vs. 67km unoptimized)',
            'Time savings: 1.5 hours per distribution run',
            'All 7 facilities restocked same day',
            'Zero stockouts in Muhanga District for insulin'
          ]
        }
      }
    ]
  },
  {
    id: 'agriculture',
    name: 'Agriculture',
    icon: Tractor,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    description: 'Maximizing Agricultural Productivity Through Intelligent Logistics',
    overview: 'Rwanda\'s agricultural sector feeds the nation and drives rural economy. Ngabo GIS empowers farmers, cooperatives, and agribusinesses with optimized farm-to-market logistics, yield mapping, and input distribution—reducing post-harvest losses and increasing profitability.',
    challenges: [
      'Smallholder farmers lose 20-30% of produce due to inefficient transportation',
      'Input suppliers struggle with last-mile distribution to remote farms',
      'Cooperatives lack tools for route optimization, leading to high fuel costs',
      'Limited visibility into optimal collection routes from scattered farms',
      'Difficulty matching farm locations with market demand'
    ],
    impact: [
      'Average 25% post-harvest loss due to delayed transport',
      'Farmers receive 30% less for produce due to middleman inefficiencies',
      'Input distribution costs consume 15-20% of farmer budgets',
      'Cooperatives spend 40% more on fuel than necessary'
    ],
    metrics: [
      { label: 'Post-Harvest Loss Reduction', value: '25%', description: 'through optimized collection routes' },
      { label: 'Farmer Income Increase', value: '30%', description: 'by eliminating middleman inefficiencies' },
      { label: 'Input Distribution Savings', value: '35%', description: 'reduction in distribution costs' },
      { label: 'Fuel Efficiency', value: '40%', description: 'improvement in cooperative logistics' },
      { label: 'Route Optimization', value: '45%', description: 'faster than manual planning' },
      { label: 'Market Access', value: '60%', description: 'improvement in farm-to-market connectivity' }
    ],
    workflows: [
      {
        id: 1,
        title: 'Cooperative Milk Collection Optimization',
        scenario: 'Dairy cooperative collecting milk from 45 smallholder farms in Musanze District',
        steps: [
          {
            title: 'FARM GEOCODING',
            description: 'Upload CSV with farm details and auto-geocode locations',
            details: 'Success rate: 44/45 farms (97.8%), manual geocoding for 1 ambiguous address',
            time: 'Batch process'
          },
          {
            title: 'DATA ENRICHMENT',
            description: 'Add custom fields for operational data',
            details: 'Daily milk volume, collection windows (6-9 AM), road conditions, storage capacity',
            time: 'Configuration'
          },
          {
            title: 'VRP OPTIMIZATION',
            description: 'Vehicle Routing Problem with constraints',
            details: '3 trucks, 500L capacity each, time windows, perishability constraints, road condition factors',
            time: '3.2 seconds'
          }
        ],
        outcome: {
          title: 'Cooperative Success',
          metrics: [
            'Collection time reduced from 6 hours to 2.1 hours per truck',
            'Total distance: 115km vs. 185km (38% reduction)',
            'Fuel savings: 42,000 RWF/day',
            'Milk spoilage reduced from 12% to 2%',
            'All farms covered in optimal sequence'
          ]
        }
      }
    ]
  },
  {
    id: 'logistics',
    name: 'Logistics',
    icon: Truck,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    description: 'Last-Mile Delivery Optimization with Real-Time Tracking',
    overview: 'Rwanda\'s growing e-commerce and delivery sector requires efficient last-mile solutions. Ngabo GIS enables logistics companies to optimize delivery routes, track vehicles in real-time, and provide accurate ETAs to customers.',
    challenges: [
      'Manual route planning leads to inefficient delivery sequences',
      'Limited real-time visibility into vehicle locations',
      'Difficulty providing accurate delivery time estimates',
      'High fuel costs due to suboptimal routing',
      'Customer complaints about delayed deliveries'
    ],
    metrics: [
      { label: 'Delivery Speed', value: '50%', description: 'faster deliveries through optimization' },
      { label: 'Fuel Efficiency', value: '35%', description: 'reduction in fuel consumption' },
      { label: 'Customer Satisfaction', value: '85%', description: 'improvement in delivery accuracy' },
      { label: 'Route Optimization', value: '60%', description: 'better than manual planning' }
    ],
    workflows: [
      {
        id: 1,
        title: 'E-commerce Delivery Optimization',
        scenario: 'Daily delivery of 50 packages across Kigali',
        steps: [
          {
            title: 'ORDER PROCESSING',
            description: 'Import delivery addresses and package details',
            details: 'CSV upload with customer addresses, package weights, delivery time preferences',
            time: 'Batch import'
          },
          {
            title: 'ROUTE OPTIMIZATION',
            description: 'TSP solving with vehicle constraints',
            details: 'Vehicle capacity: 100 packages, working hours: 8AM-6PM, traffic considerations',
            time: '4.5 seconds'
          },
          {
            title: 'REAL-TIME TRACKING',
            description: 'Live vehicle monitoring and customer updates',
            details: 'GPS tracking, automatic SMS notifications, ETA updates',
            time: 'Continuous'
          }
        ],
        outcome: {
          title: 'Delivery Excellence',
          metrics: [
            'Average delivery time: 3.2 hours vs. 5.8 hours',
            'Fuel consumption: 65% of previous usage',
            'Customer satisfaction: 94% (vs. 67%)',
            'On-time delivery rate: 96%'
          ]
        }
      }
    ]
  }
];

const testimonials = [
  {
    quote: "Ngabo GIS has transformed our emergency response capabilities. What used to take 45 minutes of manual coordination now happens in seconds. We're saving lives every day through optimized dispatch and supply chain management.",
    author: "Dr. Jean Baptiste Mazimpaka",
    title: "Director of Emergency Services",
    organization: "Rwanda Biomedical Center",
    sector: "Health"
  },
  {
    quote: "Our cooperative has reduced milk collection time by 60% and eliminated spoilage. The route optimization is incredible - it understands our local roads and constraints perfectly.",
    author: "Marie Uwimana",
    title: "Cooperative Manager",
    organization: "Musanze Dairy Cooperative",
    sector: "Agriculture"
  },
  {
    quote: "Delivery efficiency has improved dramatically. Our customers now receive accurate ETAs and we've cut fuel costs by a third. The real-time tracking gives us complete visibility.",
    author: "Patrick Nkurunziza",
    title: "Operations Director",
    organization: "Kigali Express Delivery",
    sector: "Logistics"
  }
];

const sectorMetrics = {
  totalSectors: 10,
  activeUsers: '6,000+',
  dailyOptimizations: '1M+',
  accuracyMaintained: '99.7%'
};
</script>

<template>
  <Head title="Use Cases - Ngabo GIS" />
  <AppLayout>
    <div class="min-h-screen bg-cta-background-two">
      <!-- Hero Section -->
      <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="py-16 lg:py-24">
            <div class="text-center">
              <h1 class="text-4xl lg:text-6xl font-bold text-gorilla-primary-three leading-tight mb-6">
                Proven Solutions for Rwanda's Critical Sectors
              </h1>
              <p class="text-xl text-gray-600 max-w-4xl mx-auto mb-8">
                From saving lives through optimized ambulance dispatch to maximizing farm yields through intelligent logistics,
                Ngabo GIS delivers measurable results across industries.
              </p>

              <!-- Sector Metrics Banner -->
              <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-3xl mx-auto">
                <div class="text-center">
                  <div class="text-3xl font-bold text-gorilla-primary">{{ sectorMetrics.totalSectors }}</div>
                  <div class="text-sm text-gray-600">Sectors Served</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-gorilla-primary">{{ sectorMetrics.activeUsers }}</div>
                  <div class="text-sm text-gray-600">Active Users</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-gorilla-primary">{{ sectorMetrics.dailyOptimizations }}</div>
                  <div class="text-sm text-gray-600">Daily Route Optimizations</div>
                </div>
                <div class="text-center">
                  <div class="text-3xl font-bold text-gorilla-primary">{{ sectorMetrics.accuracyMaintained }}</div>
                  <div class="text-sm text-gray-600">Accuracy Maintained</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Interactive Sector Navigator -->
      <div class="py-8">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gorilla-primary-three mb-4">
              Explore Solutions by Sector
            </h2>
          </div>

          <!-- Sector Grid -->
          <div class="grid grid-cols-2 md:grid-cols-5 lg:grid-cols-10 gap-4 mb-8">
            <button
              v-for="sector in sectors.slice(0, 3)"
              :key="sector.id"
              @click="activeSector = sector.id"
              :class="[
                'p-4 rounded-xl transition-all duration-200 text-center',
                activeSector === sector.id
                  ? `${sector.bgColor} ${sector.borderColor} border-2`
                  : 'bg-white border border-gray-200 hover:border-gorilla-primary'
              ]"
            >
              <component
                :is="sector.icon"
                :class="[
                  'w-8 h-8 mx-auto mb-2',
                  activeSector === sector.id ? sector.color : 'text-gray-600'
                ]"
              />
              <div :class="[
                'text-sm font-medium',
                activeSector === sector.id ? 'text-gorilla-primary-three' : 'text-gray-600'
              ]">
                {{ sector.name }}
              </div>
            </button>

            <!-- Placeholder for other sectors -->
            <div class="col-span-2 md:col-span-2 lg:col-span-7 flex items-center justify-center">
              <div class="text-center p-4">
                <div class="text-sm text-gray-500 mb-2">+ 7 More Sectors</div>
                <div class="text-xs text-gray-400">Government • Mining • Education • Tourism • Real Estate • Fintech • Utilities</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Detailed Sector Content -->
      <div class="py-16">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div
            v-for="sector in sectors"
            :key="sector.id"
            v-show="activeSector === sector.id"
            class="space-y-12"
          >
            <!-- Sector Header -->
            <div class="text-center">
              <div class="flex items-center justify-center space-x-4 mb-6">
                <div :class="[
                  'w-16 h-16 rounded-2xl flex items-center justify-center',
                  sector.bgColor
                ]">
                  <component :is="sector.icon" :class="['w-8 h-8', sector.color]" />
                </div>
                <div class="text-left">
                  <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three">{{ sector.name }}</h2>
                  <p class="text-xl text-gray-600">{{ sector.description }}</p>
                </div>
              </div>
              <p class="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
                {{ sector.overview }}
              </p>
            </div>

            <!-- Challenge & Impact -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Challenges -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary-three flex items-center space-x-2">
                    <Target class="w-6 h-6 text-red-500" />
                    <span>The Challenge</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul class="space-y-3">
                    <li
                      v-for="challenge in sector.challenges"
                      :key="challenge"
                      class="flex items-start space-x-3"
                    >
                      <div class="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span class="text-gray-600">{{ challenge }}</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <!-- Impact Without Solution -->
              <Card>
                <CardHeader>
                  <CardTitle class="text-gorilla-primary-three flex items-center space-x-2">
                    <TrendingUp class="w-6 h-6 text-orange-500" />
                    <span>Impact Without Solution</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul class="space-y-3">
                    <li
                      v-for="impact in sector.impact"
                      :key="impact"
                      class="flex items-start space-x-3"
                    >
                      <div class="w-2 h-2 bg-orange-500 rounded-full mt-2 flex-shrink-0"></div>
                      <span class="text-gray-600">{{ impact }}</span>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>

            <!-- Solution Overview -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                  <CheckCircle class="w-6 h-6" />
                  <span>Three-Engine Integration Solution</span>
                </CardTitle>
                <CardDescription>
                  How Ngabo GIS's three proprietary engines work together to solve {{ sector.name.toLowerCase() }} sector challenges
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <!-- Indexing Engine -->
                  <div class="text-center p-6 bg-cta-background-one rounded-xl">
                    <div class="w-12 h-12 bg-gorilla-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                      <Search class="w-6 h-6 text-white" />
                    </div>
                    <h4 class="font-semibold text-gorilla-primary-three mb-2">Indexing Engine</h4>
                    <p class="text-sm text-gray-600">Location Intelligence & Pattern Recognition</p>
                  </div>

                  <!-- Data Engine -->
                  <div class="text-center p-6 bg-cta-background-one rounded-xl">
                    <div class="w-12 h-12 bg-gorilla-primary-two rounded-xl flex items-center justify-center mx-auto mb-4">
                      <Database class="w-6 h-6 text-white" />
                    </div>
                    <h4 class="font-semibold text-gorilla-primary-three mb-2">Data Engine</h4>
                    <p class="text-sm text-gray-600">Spatial Analytics & Data Fusion</p>
                  </div>

                  <!-- Travel Engine -->
                  <div class="text-center p-6 bg-cta-background-one rounded-xl">
                    <div class="w-12 h-12 bg-gorilla-primary-three rounded-xl flex items-center justify-center mx-auto mb-4">
                      <Navigation class="w-6 h-6 text-white" />
                    </div>
                    <h4 class="font-semibold text-gorilla-primary-three mb-2">Travel Engine</h4>
                    <p class="text-sm text-gray-600">Route Optimization & Real-time Tracking</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Real-World Workflows -->
            <div class="space-y-8">
              <div class="text-center">
                <h3 class="text-2xl font-bold text-gorilla-primary-three mb-4">Real-World Workflows</h3>
                <p class="text-gray-600">Step-by-step examples of how Ngabo GIS solves real {{ sector.name.toLowerCase() }} challenges</p>
              </div>

              <!-- Workflow Navigation -->
              <div v-if="sector.workflows.length > 1" class="flex justify-center">
                <div class="bg-white border border-gray-200 rounded-full p-1 inline-flex">
                  <button
                    v-for="workflow in sector.workflows"
                    :key="workflow.id"
                    @click="activeWorkflow = workflow.id"
                    :class="[
                      'px-4 py-2 rounded-full text-sm font-medium transition-all duration-200',
                      activeWorkflow === workflow.id
                        ? 'bg-gorilla-primary text-white'
                        : 'text-gorilla-primary-three hover:bg-cta-background-one'
                    ]"
                  >
                    Workflow {{ workflow.id }}
                  </button>
                </div>
              </div>

              <!-- Workflow Details -->
              <div
                v-for="workflow in sector.workflows"
                :key="workflow.id"
                v-show="activeWorkflow === workflow.id"
              >
                <Card>
                  <CardHeader>
                    <CardTitle class="text-gorilla-primary-three">{{ workflow.title }}</CardTitle>
                    <CardDescription class="text-lg">{{ workflow.scenario }}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <!-- Workflow Steps -->
                    <div class="space-y-6">
                      <div
                        v-for="(step, index) in workflow.steps"
                        :key="index"
                        class="relative"
                      >
                        <!-- Step Connector Line -->
                        <div
                          v-if="index < workflow.steps.length - 1"
                          class="absolute left-6 top-12 w-0.5 h-16 bg-gray-200"
                        ></div>

                        <div class="flex items-start space-x-4">
                          <!-- Step Number -->
                          <div class="w-12 h-12 bg-gorilla-primary rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                            {{ index + 1 }}
                          </div>

                          <!-- Step Content -->
                          <div class="flex-1">
                            <div class="bg-white border border-gray-200 rounded-xl p-6">
                              <div class="flex items-center justify-between mb-3">
                                <h4 class="font-semibold text-gorilla-primary-three">{{ step.title }}</h4>
                                <div class="text-sm font-medium text-gorilla-primary bg-gorilla-primary/10 px-3 py-1 rounded-full">
                                  {{ step.time }}
                                </div>
                              </div>
                              <p class="text-gray-600 mb-3">{{ step.description }}</p>
                              <p class="text-sm text-gray-500">{{ step.details }}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- Workflow Outcome -->
                    <div class="mt-8 bg-green-50 border border-green-200 rounded-xl p-6">
                      <h4 class="font-semibold text-green-800 mb-4 flex items-center space-x-2">
                        <Award class="w-5 h-5" />
                        <span>{{ workflow.outcome.title }}</span>
                      </h4>
                      <ul class="space-y-2">
                        <li
                          v-for="metric in workflow.outcome.metrics"
                          :key="metric"
                          class="flex items-start space-x-2"
                        >
                          <CheckCircle class="w-4 h-4 text-green-600 mt-0.5 flex-shrink-0" />
                          <span class="text-green-700 text-sm">{{ metric }}</span>
                        </li>
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>

            <!-- Measurable Results -->
            <div>
              <div class="text-center mb-8">
                <h3 class="text-2xl font-bold text-gorilla-primary-three mb-4">Measurable Results</h3>
                <p class="text-gray-600">Proven performance metrics from real {{ sector.name.toLowerCase() }} implementations</p>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div
                  v-for="metric in sector.metrics"
                  :key="metric.label"
                  class="text-center p-6 bg-white border border-gray-200 rounded-xl hover:border-gorilla-primary transition-colors duration-200"
                >
                  <div class="text-3xl font-bold text-gorilla-primary mb-2">{{ metric.value }}</div>
                  <div class="font-semibold text-gorilla-primary-three mb-1">{{ metric.label }}</div>
                  <div class="text-sm text-gray-600">{{ metric.description }}</div>
                </div>
              </div>
            </div>

            <!-- Customer Testimonial -->
            <Card v-if="testimonials.find(t => t.sector === sector.name)">
              <CardContent class="p-8">
                <div class="text-center">
                  <Quote class="w-12 h-12 text-gorilla-primary mx-auto mb-6" />
                  <blockquote class="text-xl text-gray-600 italic mb-6 leading-relaxed">
                    "{{ testimonials.find(t => t.sector === sector.name)?.quote }}"
                  </blockquote>
                  <div class="flex items-center justify-center space-x-4">
                    <div class="w-12 h-12 bg-gorilla-primary rounded-full flex items-center justify-center">
                      <Users class="w-6 h-6 text-white" />
                    </div>
                    <div class="text-left">
                      <div class="font-semibold text-gorilla-primary-three">
                        {{ testimonials.find(t => t.sector === sector.name)?.author }}
                      </div>
                      <div class="text-sm text-gray-600">
                        {{ testimonials.find(t => t.sector === sector.name)?.title }}
                      </div>
                      <div class="text-sm text-gorilla-primary">
                        {{ testimonials.find(t => t.sector === sector.name)?.organization }}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Technical Implementation -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                  <Globe class="w-6 h-6" />
                  <span>Technical Implementation</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div class="text-center">
                    <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Globe class="w-6 h-6 text-gorilla-primary" />
                    </div>
                    <div class="font-semibold text-gorilla-primary-three mb-1">APIs & Integration</div>
                    <div class="text-sm text-gray-600">REST API, WebSocket, CSV/Excel import, GeoJSON export</div>
                  </div>
                  <div class="text-center">
                    <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Database class="w-6 h-6 text-gorilla-primary" />
                    </div>
                    <div class="font-semibold text-gorilla-primary-three mb-1">Data Sources</div>
                    <div class="text-sm text-gray-600">Ministry databases, facility registries, mobile app data</div>
                  </div>
                  <div class="text-center">
                    <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Shield class="w-6 h-6 text-gorilla-primary" />
                    </div>
                    <div class="font-semibold text-gorilla-primary-three mb-1">Deployment</div>
                    <div class="text-sm text-gray-600">Cloud-hosted, on-premises option, mobile SDKs</div>
                  </div>
                  <div class="text-center">
                    <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                      <Activity class="w-6 h-6 text-gorilla-primary" />
                    </div>
                    <div class="font-semibold text-gorilla-primary-three mb-1">Real-time</div>
                    <div class="text-sm text-gray-600">Live tracking, instant notifications, dashboard monitoring</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Sector CTA -->
            <div class="text-center">
              <Card class="bg-gradient-to-r from-gorilla-primary/5 to-gorilla-primary-two/5 border-gorilla-primary">
                <CardContent class="p-8">
                  <h3 class="text-2xl font-bold text-gorilla-primary-three mb-4">
                    Discover How Ngabo GIS Can Optimize Your {{ sector.name }} Operations
                  </h3>
                  <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white px-8 py-3">
                      Schedule {{ sector.name }} Sector Demo
                      <ArrowRight class="ml-2 h-5 w-5" />
                    </Button>
                    <Button variant="outline" class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white px-8 py-3">
                      <Download class="mr-2 h-5 w-5" />
                      Download {{ sector.name }} Use Case PDF
                    </Button>
                    <Button variant="outline" class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white px-8 py-3">
                      <Share2 class="mr-2 h-5 w-5" />
                      Contact {{ sector.name }} Solutions Team
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <!-- All Sectors Overview -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Comprehensive Sector Coverage
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              Ngabo GIS delivers specialized solutions across all critical sectors of Rwanda's economy
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <!-- Health -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-red-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Hospital class="w-6 h-6 text-red-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Health</h3>
                <p class="text-sm text-gray-600 mb-3">Emergency dispatch, supply chain, outbreak tracking</p>
                <div class="text-sm font-medium text-red-600">40% faster response times</div>
              </CardContent>
            </Card>

            <!-- Agriculture -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-green-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Tractor class="w-6 h-6 text-green-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Agriculture</h3>
                <p class="text-sm text-gray-600 mb-3">Farm-to-market logistics, yield optimization</p>
                <div class="text-sm font-medium text-green-600">30% reduction in transport costs</div>
              </CardContent>
            </Card>

            <!-- Logistics -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-blue-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Truck class="w-6 h-6 text-blue-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Logistics</h3>
                <p class="text-sm text-gray-600 mb-3">Last-mile delivery, real-time tracking</p>
                <div class="text-sm font-medium text-blue-600">50% faster deliveries</div>
              </CardContent>
            </Card>

            <!-- Government -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-purple-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Building class="w-6 h-6 text-purple-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Government</h3>
                <p class="text-sm text-gray-600 mb-3">Citizen services, resource allocation</p>
                <div class="text-sm font-medium text-purple-600">60% increase in service awareness</div>
              </CardContent>
            </Card>

            <!-- Mining -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Wrench class="w-6 h-6 text-orange-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Mining</h3>
                <p class="text-sm text-gray-600 mb-3">Resource mapping, supply coordination</p>
                <div class="text-sm font-medium text-orange-600">Resource optimization</div>
              </CardContent>
            </Card>

            <!-- Education -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-indigo-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <GraduationCap class="w-6 h-6 text-indigo-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Education</h3>
                <p class="text-sm text-gray-600 mb-3">School optimization, student transport</p>
                <div class="text-sm font-medium text-indigo-600">Route optimization</div>
              </CardContent>
            </Card>

            <!-- Tourism -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-pink-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Camera class="w-6 h-6 text-pink-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Tourism</h3>
                <p class="text-sm text-gray-600 mb-3">Attraction mapping, tour routes</p>
                <div class="text-sm font-medium text-pink-600">Tour optimization</div>
              </CardContent>
            </Card>

            <!-- Real Estate -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-teal-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Home class="w-6 h-6 text-teal-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Real Estate</h3>
                <p class="text-sm text-gray-600 mb-3">Location intelligence, market analysis</p>
                <div class="text-sm font-medium text-teal-600">Market analysis</div>
              </CardContent>
            </Card>

            <!-- Fintech -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-yellow-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Smartphone class="w-6 h-6 text-yellow-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Fintech</h3>
                <p class="text-sm text-gray-600 mb-3">Branch optimization, agent planning</p>
                <div class="text-sm font-medium text-yellow-600">Network optimization</div>
              </CardContent>
            </Card>

            <!-- Utilities -->
            <Card class="text-center p-6 hover:shadow-lg transition-shadow duration-200">
              <CardContent class="p-0">
                <div class="w-12 h-12 bg-cyan-50 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Zap class="w-6 h-6 text-cyan-600" />
                </div>
                <h3 class="font-semibold text-gorilla-primary-three mb-2">Utilities</h3>
                <p class="text-sm text-gray-600 mb-3">Infrastructure mapping, coverage analysis</p>
                <div class="text-sm font-medium text-cyan-600">Coverage analysis</div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <!-- Platform Integration -->
      <div class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Built to Integrate with Your Technology Stack
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              RESTful APIs, comprehensive SDKs, and extensive documentation for seamless integration across all sectors
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Integration Highlights -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary">Integration Capabilities</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-6">
                  <div class="flex items-start space-x-4">
                    <div class="w-10 h-10 bg-gorilla-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Globe class="w-5 h-5 text-gorilla-primary" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-1">API Documentation</h4>
                      <p class="text-sm text-gray-600">OpenAPI specification, interactive explorer, code examples in multiple languages</p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-4">
                    <div class="w-10 h-10 bg-gorilla-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Smartphone class="w-5 h-5 text-gorilla-primary" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-1">Mobile SDKs</h4>
                      <p class="text-sm text-gray-600">iOS, Android, and Web SDKs with offline search and custom UI components</p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-4">
                    <div class="w-10 h-10 bg-gorilla-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Database class="w-5 h-5 text-gorilla-primary" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-1">Data Import/Export</h4>
                      <p class="text-sm text-gray-600">CSV, Excel, GeoJSON, Shapefiles, GPX, PDF - industry-standard formats</p>
                    </div>
                  </div>
                  <div class="flex items-start space-x-4">
                    <div class="w-10 h-10 bg-gorilla-primary/10 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Activity class="w-5 h-5 text-gorilla-primary" />
                    </div>
                    <div>
                      <h4 class="font-semibold text-gorilla-primary-three mb-1">Real-time Protocols</h4>
                      <p class="text-sm text-gray-600">HTTPS for REST APIs, WebSocket for real-time updates, gRPC for high-performance</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Rate Limiting -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary">Rate Limiting & Fair Use</CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700">Developer</span>
                    <span class="text-sm text-gray-600">100 requests/day, 50/minute</span>
                  </div>
                  <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700">Pay-As-You-Go</span>
                    <span class="text-sm text-gray-600">Based on usage</span>
                  </div>
                  <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700">Self-Serve Unlimited</span>
                    <span class="text-sm text-gray-600">3,000 lookups/minute</span>
                  </div>
                  <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="font-medium text-gray-700">Enterprise</span>
                    <span class="text-sm text-gray-600">Custom limits</span>
                  </div>
                  <div class="flex justify-between items-center py-2">
                    <span class="font-medium text-gray-700">On-Premises</span>
                    <span class="text-sm text-gray-600">No limits</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <!-- Final CTA -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-6">
              Ready to Transform Your Sector with Ngabo GIS?
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-12">
              Join 6,000+ users across Rwanda who are already optimizing their operations with our geospatial intelligence platform.
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
              <!-- Explore More Use Cases -->
              <Card class="text-center p-6">
                <CardContent class="p-0">
                  <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Eye class="w-8 h-8 text-gorilla-primary" />
                  </div>
                  <h3 class="font-semibold text-gorilla-primary-three mb-3">Explore More Use Cases</h3>
                  <p class="text-sm text-gray-600 mb-4">Discover detailed workflows for all 10 sectors</p>
                  <Button variant="outline" class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white">
                    View All Sectors
                  </Button>
                </CardContent>
              </Card>

              <!-- Start Building -->
              <Card class="text-center p-6 border-2 border-gorilla-primary">
                <CardContent class="p-0">
                  <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Zap class="w-8 h-8 text-gorilla-primary" />
                  </div>
                  <h3 class="font-semibold text-gorilla-primary-three mb-3">Start Building Today</h3>
                  <p class="text-sm text-gray-600 mb-4">Free API access for developers and small projects</p>
                  <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                    Get Free API Key
                    <ArrowRight class="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>

              <!-- Enterprise Demo -->
              <Card class="text-center p-6">
                <CardContent class="p-0">
                  <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <Users class="w-8 h-8 text-gorilla-primary" />
                  </div>
                  <h3 class="font-semibold text-gorilla-primary-three mb-3">Enterprise Demo</h3>
                  <p class="text-sm text-gray-600 mb-4">Custom solutions for large organizations</p>
                  <Button variant="outline" class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white">
                    Schedule Demo
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
