<script setup>
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { ref, computed } from 'vue';
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  CheckCircle,
  X,
  Star,
  Calculator,
  CreditCard,
  Building,
  Shield,
  Server,
  Users,
  ArrowRight,
  HelpCircle,
  DollarSign,
  FileText,
  Handshake,
  Calendar,
  Zap,
  Clock,
  Target,
  Activity,
  TrendingUp,
  Layers,
  Globe,
  Lock,
  Phone,
  Mail,
  MessageSquare
} from 'lucide-vue-next';

// Reactive data
const billingCycle = ref('monthly');
const calculatorQueries = ref(10000);
const showCalculator = ref(true);

// Computed values
const calculatedCost = computed(() => {
  if (calculatorQueries.value <= 1000) return 0;
  return ((calculatorQueries.value - 1000) / 1000) * 0.10;
});

const recommendedTier = computed(() => {
  if (calculatorQueries.value <= 100) return 'Developer';
  if (calculatorQueries.value <= 100000) return 'Pay-As-You-Go';
  if (calculatorQueries.value <= 1000000) return 'Self-Serve Unlimited';
  return 'Enterprise Unlimited';
});

// Pricing tiers data
const pricingTiers = [
  {
    id: 'developer',
    name: 'Developer',
    badge: 'FREE • Perfect for Prototyping',
    price: { monthly: 0, annual: 0 },
    description: 'Forever free',
    limits: {
      daily: '100 requests per day',
      minute: '50 requests per minute'
    },
    features: [
      'Indexing Engine: Search & geocoding API',
      'Data Engine: Basic custom map creation',
      'Travel Engine: Simple routing (up to 10 waypoints)',
      'Public POI database access',
      'GeoJSON export',
      'API documentation access',
      'Community support (forum)',
      '99.5% uptime'
    ],
    idealFor: 'Individual developers, students, and small projects testing integration',
    cta: 'Get Free API Key',
    ctaVariant: 'default',
    popular: false
  },
  {
    id: 'payg',
    name: 'Pay-As-You-Go',
    badge: 'FLEXIBLE • Pay Only for What You Use',
    price: { monthly: 0.10, annual: 0.10 },
    description: '$0.10 per 1,000 lookups',
    limits: {
      daily: 'No daily limit',
      minute: '100 requests per minute'
    },
    features: [
      'All Developer features, plus:',
      'Unlimited daily requests (pay-per-use)',
      'Advanced routing (up to 50 waypoints)',
      'Custom field creation',
      'Data imports (CSV, Excel)',
      'Basic analytics (view only)',
      'Email support (48-hour response)',
      '99.7% uptime',
      'Add-on: Basic geofencing ($50/month per 10 items)'
    ],
    idealFor: 'Small to medium businesses, freelancers, and ad-hoc projects with variable usage',
    cta: 'Start Free Trial',
    ctaVariant: 'default',
    popular: false
  },
  {
    id: 'unlimited',
    name: 'Self-Serve Unlimited',
    badge: 'MOST POPULAR • High-Volume Operations',
    price: { monthly: 1000, annual: 950 },
    description: 'Dedicated instance',
    limits: {
      daily: 'No daily or monthly limits',
      minute: '3,000 requests per minute per instance'
    },
    features: [
      'All Pay-As-You-Go features, plus:',
      'Dedicated server instance',
      'Advanced geofencing (unlimited items)',
      'Real-time asset tracking via WebSocket',
      'Advanced analytics suite (heatmaps, choropleths)',
      'TSP/VRP optimization (150 waypoints)',
      'Bulk geocoding (10,000 queries/call)',
      'Priority email support (24-hour response)',
      'Phone support (business hours)',
      '99.9% uptime guarantee',
      'Monthly usage reports'
    ],
    idealFor: 'Logistics companies, tech startups, SMEs with high-throughput geocoding and routing needs',
    cta: 'Start 14-Day Free Trial',
    ctaVariant: 'default',
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise Unlimited',
    badge: 'MISSION-CRITICAL • Full Compliance',
    price: { monthly: 2500, annual: 2500 },
    description: 'Custom volumes available',
    limits: {
      daily: 'Unlimited requests',
      minute: 'Custom rate limits (millions/second available)'
    },
    features: [
      'All Self-Serve Unlimited features, plus:',
      'SOC 2 Type 2 audited',
      'HIPAA/HITECH compliant',
      '99.9% uptime SLA with penalties',
      'SAML Single Sign-On (SSO)',
      'Data residency guarantee (Rwanda)',
      'Dedicated account manager',
      'Custom API rate limits',
      '24/7 phone and email support',
      '2-hour critical issue response time',
      'Quarterly business reviews',
      'Advanced audit logging'
    ],
    idealFor: 'Large enterprises, government agencies, NGOs with strict security and compliance requirements',
    cta: 'Contact Sales',
    ctaVariant: 'outline',
    popular: false
  },
  {
    id: 'onpremises',
    name: 'On-Premises',
    badge: 'SOVEREIGN • Complete Control',
    price: { monthly: 2500, annual: 2500 },
    description: '$30,000/year per instance',
    limits: {
      daily: 'Unlimited users and requests',
      minute: 'No external dependencies'
    },
    features: [
      'All Enterprise Unlimited features, plus:',
      'Full platform as Docker image',
      'Deploy on your own infrastructure',
      'Complete offline functionality',
      'Self-hosted Nominatim and OSRM',
      'No data leaves your network',
      'White-glove installation support',
      'Dedicated technical account manager',
      'Priority software updates',
      'Custom feature development (negotiable)',
      '24/7 emergency support hotline'
    ],
    idealFor: 'Government agencies, defense, critical infrastructure, organizations requiring absolute data sovereignty',
    cta: 'Request On-Premises Quote',
    ctaVariant: 'outline',
    popular: false
  }
];

// FAQ data
const faqs = [
  {
    question: 'What happens if I exceed my tier\'s rate limits?',
    answer: 'For Developer tier, requests beyond limits receive HTTP 429 errors. For paid tiers, Pay-As-You-Go automatically scales (you pay per use), while Self-Serve and above have soft limits with notifications before throttling. Enterprise and On-Premises have custom arrangements.'
  },
  {
    question: 'Can I switch between tiers mid-billing cycle?',
    answer: 'Yes. Upgrades take effect immediately with prorated billing. Downgrades take effect at the next billing cycle. Unused credits roll over for 12 months.'
  },
  {
    question: 'Do you offer discounts for education, research, or non-profits?',
    answer: 'Yes. Educational institutions and registered NGOs working on public-good projects may qualify for discounts up to 50%. Contact our Government & NGO Relations team.'
  },
  {
    question: 'What payment methods do you accept?',
    answer: 'Credit/debit cards (Visa, Mastercard), bank transfers (RWF/USD), purchase orders for government/enterprise clients, and mobile money (MTN, Airtel) for Rwandan customers.'
  },
  {
    question: 'Is there a setup fee or long-term contract required?',
    answer: 'No setup fees for any tier. Developer and Pay-As-You-Go have no contract. Self-Serve Unlimited is month-to-month (annual option for discount). Enterprise typically involves annual contracts but flexible terms available. On-Premises requires annual commitment.'
  }
];

const formatPrice = (price, cycle) => {
  if (price === 0) return '$0';
  if (cycle === 'annual') return `$${(price * 12 * 0.95).toLocaleString()}`;
  return `$${price.toLocaleString()}`;
};
</script>

<template>
  <Head title="Pricing - Ngabo GIS" />
  <AppLayout>
    <div class="min-h-screen bg-cta-background-two">
      <!-- Hero Section -->
      <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="py-16 lg:py-24">
            <div class="text-center">
              <h1 class="text-4xl lg:text-6xl font-bold text-gorilla-primary-three leading-tight mb-6">
                Transparent Pricing for Every Scale
              </h1>
              <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
                From free developer access to sovereign on-premises deployments. No hidden fees. No surprises.
              </p>
              <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                Whether you're prototyping an idea or running mission-critical government operations, Ngabo GIS scales with your needs.
              </p>
            </div>

            <!-- Interactive Pricing Calculator -->
            <div v-if="showCalculator" class="mt-12 max-w-2xl mx-auto">
              <Card class="border-2 border-gorilla-primary">
                <CardHeader class="text-center">
                  <CardTitle class="text-gorilla-primary flex items-center justify-center space-x-2">
                    <Calculator class="w-6 h-6" />
                    <span>Pricing Calculator</span>
                  </CardTitle>
                  <CardDescription>Find your perfect tier based on usage</CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="space-y-6">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-2">
                        How many lookups per month?
                      </label>
                      <input
                        v-model.number="calculatorQueries"
                        type="range"
                        min="0"
                        max="10000000"
                        step="1000"
                        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                      />
                      <div class="flex justify-between text-sm text-gray-500 mt-1">
                        <span>0</span>
                        <span>10M+</span>
                      </div>
                      <div class="text-center mt-2">
                        <span class="text-2xl font-bold text-gorilla-primary">{{ calculatorQueries.toLocaleString() }}</span>
                        <span class="text-gray-600"> lookups/month</span>
                      </div>
                    </div>

                    <div class="bg-cta-background-one rounded-xl p-6">
                      <div class="text-center">
                        <div class="text-sm font-medium text-gray-700 mb-2">Recommended Tier</div>
                        <div class="text-2xl font-bold text-gorilla-primary mb-2">{{ recommendedTier }}</div>
                        <div class="text-lg font-semibold text-gorilla-primary-three">
                          {{ calculatedCost > 0 ? `$${calculatedCost.toFixed(2)}/month` : 'Free' }}
                        </div>
                        <Button class="mt-4 bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                          See Detailed Breakdown
                          <ArrowRight class="ml-2 h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <!-- Billing Toggle -->
      <div class="py-8">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="flex justify-center">
            <div class="bg-white border border-gray-200 rounded-full p-1 inline-flex">
              <button
                @click="billingCycle = 'monthly'"
                :class="[
                  'px-6 py-2 rounded-full text-sm font-medium transition-all duration-200',
                  billingCycle === 'monthly'
                    ? 'bg-gorilla-primary text-white'
                    : 'text-gorilla-primary-three hover:bg-cta-background-one'
                ]"
              >
                Monthly
              </button>
              <button
                @click="billingCycle = 'annual'"
                :class="[
                  'px-6 py-2 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2',
                  billingCycle === 'annual'
                    ? 'bg-gorilla-primary text-white'
                    : 'text-gorilla-primary-three hover:bg-cta-background-one'
                ]"
              >
                <span>Annual</span>
                <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Save 5%</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pricing Tiers -->
      <div class="py-16">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
            <div
              v-for="tier in pricingTiers"
              :key="tier.id"
              :class="[
                'relative',
                tier.popular ? 'lg:scale-105 lg:-mt-4' : ''
              ]"
            >
              <Card :class="[
                'h-full',
                tier.popular ? 'border-2 border-gorilla-primary shadow-xl' : 'border border-gray-200'
              ]">
                <!-- Popular Badge -->
                <div v-if="tier.popular" class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div class="bg-gorilla-primary text-white px-4 py-2 rounded-full text-sm font-medium flex items-center space-x-1">
                    <Star class="w-4 h-4" />
                    <span>Most Popular</span>
                  </div>
                </div>

                <CardHeader class="text-center pb-4">
                  <div class="text-sm font-medium text-gorilla-primary bg-gorilla-primary/10 px-3 py-1 rounded-full inline-block mb-3">
                    {{ tier.badge }}
                  </div>
                  <CardTitle class="text-2xl font-bold text-gorilla-primary-three">{{ tier.name }}</CardTitle>
                  <div class="mt-4">
                    <div class="text-4xl font-bold text-gorilla-primary-three">
                      {{ formatPrice(tier.price[billingCycle], billingCycle) }}
                    </div>
                    <div class="text-sm text-gray-600 mt-1">
                      {{ billingCycle === 'annual' && tier.price.monthly > 0 ? '/year' : tier.price.monthly > 0 ? '/month' : '' }}
                    </div>
                    <div class="text-sm text-gray-600 mt-1">{{ tier.description }}</div>
                  </div>
                </CardHeader>

                <CardContent class="space-y-6">
                  <!-- Rate Limits -->
                  <div>
                    <h4 class="font-semibold text-gorilla-primary-three mb-3">Rate Limits</h4>
                    <div class="space-y-2 text-sm">
                      <div class="flex justify-between">
                        <span class="text-gray-600">Daily:</span>
                        <span class="font-medium">{{ tier.limits.daily }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span class="text-gray-600">Per minute:</span>
                        <span class="font-medium">{{ tier.limits.minute }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Features -->
                  <div>
                    <h4 class="font-semibold text-gorilla-primary-three mb-3">Included Features</h4>
                    <ul class="space-y-2">
                      <li
                        v-for="feature in tier.features"
                        :key="feature"
                        class="flex items-start space-x-2 text-sm"
                      >
                        <CheckCircle class="w-4 h-4 text-gorilla-primary mt-0.5 flex-shrink-0" />
                        <span class="text-gray-600">{{ feature }}</span>
                      </li>
                    </ul>
                  </div>

                  <!-- Ideal For -->
                  <div class="bg-cta-background-one rounded-lg p-4">
                    <h4 class="font-semibold text-gorilla-primary-three mb-2">Ideal For</h4>
                    <p class="text-sm text-gray-600">{{ tier.idealFor }}</p>
                  </div>

                  <!-- CTA -->
                  <div class="pt-4">
                    <Button
                      :variant="tier.ctaVariant"
                      :class="[
                        'w-full',
                        tier.ctaVariant === 'default'
                          ? 'bg-gorilla-primary hover:bg-gorilla-primary/90 text-white'
                          : 'border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white'
                      ]"
                    >
                      {{ tier.cta }}
                    </Button>
                    <div v-if="tier.id === 'developer'" class="text-xs text-gray-500 text-center mt-2">
                      No credit card required
                    </div>
                    <div v-else-if="tier.id === 'payg'" class="text-xs text-gray-500 text-center mt-2">
                      Cancel anytime. No minimum commitment.
                    </div>
                    <div v-else-if="tier.id === 'unlimited'" class="text-xs text-gray-500 text-center mt-2">
                      No credit card required for trial. Cancel anytime.
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <!-- Government & NGO Payment Section -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Flexible Payment Options for Public Sector & Non-Profit Organizations
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              We understand government procurement and NGO grant cycles. Ngabo GIS offers payment flexibility to support Rwanda's development goals.
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Bank Transfer -->
            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Building class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Bank Transfer</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li>Direct payments to TKD's Rwandan bank account</li>
                  <li>Invoicing in RWF or USD</li>
                  <li>Net 30 payment terms</li>
                  <li>Formal receipts provided</li>
                </ul>
              </CardContent>
            </Card>

            <!-- Purchase Orders -->
            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <FileText class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Purchase Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li>Formal procurement process supported</li>
                  <li>30-60 day payment terms</li>
                  <li>PO-to-invoice workflow</li>
                  <li>Dedicated government account team</li>
                </ul>
              </CardContent>
            </Card>

            <!-- Grants & Subsidies -->
            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Handshake class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Grants & Subsidies</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li>Support for grant-funded NGO projects</li>
                  <li>Negotiated discounts for public-good initiatives</li>
                  <li>Focus areas: health, education, agriculture</li>
                  <li>Flexible contract structures</li>
                </ul>
              </CardContent>
            </Card>

            <!-- Annual Contracts -->
            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Calendar class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Annual Contracts</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-sm text-gray-600">
                  <li>Fixed-rate agreements for predictable budgeting</li>
                  <li>Aligned with government fiscal cycles</li>
                  <li>Multi-year agreements with volume discounts</li>
                  <li>Renewal options with price lock</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <!-- Additional Benefits -->
          <Card class="mt-12">
            <CardHeader>
              <CardTitle class="text-gorilla-primary text-center">Additional Government/NGO Benefits</CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div class="flex items-start space-x-3">
                  <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span class="text-gray-600">Priority onboarding and training</span>
                </div>
                <div class="flex items-start space-x-3">
                  <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span class="text-gray-600">Custom integration support</span>
                </div>
                <div class="flex items-start space-x-3">
                  <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span class="text-gray-600">Dedicated government relations manager</span>
                </div>
                <div class="flex items-start space-x-3">
                  <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span class="text-gray-600">Quarterly usage and impact reporting</span>
                </div>
                <div class="flex items-start space-x-3">
                  <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span class="text-gray-600">Partnership opportunities for public initiatives</span>
                </div>
                <div class="flex items-start space-x-3">
                  <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span class="text-gray-600">Access to specialized modules (health, agriculture)</span>
                </div>
              </div>
              <div class="text-center mt-8">
                <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white px-8 py-3">
                  Contact Government Relations Team
                  <ArrowRight class="ml-2 h-5 w-5" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Feature Comparison Table -->
      <div class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Compare All Plans Side-by-Side
            </h2>
          </div>

          <Card>
            <CardContent class="p-0">
              <div class="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead class="w-1/4 font-semibold text-gorilla-primary-three">Features</TableHead>
                      <TableHead class="text-center font-semibold text-gorilla-primary-three">Developer</TableHead>
                      <TableHead class="text-center font-semibold text-gorilla-primary-three">Pay-As-You-Go</TableHead>
                      <TableHead class="text-center font-semibold text-gorilla-primary-three bg-gorilla-primary/5">Self-Serve Unlimited</TableHead>
                      <TableHead class="text-center font-semibold text-gorilla-primary-three">Enterprise</TableHead>
                      <TableHead class="text-center font-semibold text-gorilla-primary-three">On-Premises</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <!-- API Access & Rate Limits -->
                    <TableRow class="bg-gray-50">
                      <TableCell class="font-semibold text-gorilla-primary-three" colspan="6">API Access & Rate Limits</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Daily request limit</TableCell>
                      <TableCell class="text-center">100</TableCell>
                      <TableCell class="text-center">Unlimited</TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5">Unlimited</TableCell>
                      <TableCell class="text-center">Unlimited</TableCell>
                      <TableCell class="text-center">Unlimited</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Per-minute rate limit</TableCell>
                      <TableCell class="text-center">50</TableCell>
                      <TableCell class="text-center">100</TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5">3,000</TableCell>
                      <TableCell class="text-center">Custom</TableCell>
                      <TableCell class="text-center">Unlimited</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Bulk geocoding (queries/call)</TableCell>
                      <TableCell class="text-center">—</TableCell>
                      <TableCell class="text-center">1,000</TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5">10,000</TableCell>
                      <TableCell class="text-center">Custom</TableCell>
                      <TableCell class="text-center">Unlimited</TableCell>
                    </TableRow>

                    <!-- Core Features -->
                    <TableRow class="bg-gray-50">
                      <TableCell class="font-semibold text-gorilla-primary-three" colspan="6">Core Features</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Indexing Engine (search/geocoding)</TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Data Engine (custom maps/analytics)</TableCell>
                      <TableCell class="text-center">Basic</TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Travel Engine (routing/optimization)</TableCell>
                      <TableCell class="text-center">10 waypoints</TableCell>
                      <TableCell class="text-center">50 waypoints</TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5">150 waypoints</TableCell>
                      <TableCell class="text-center">150 waypoints</TableCell>
                      <TableCell class="text-center">Unlimited</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Real-time tracking</TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>

                    <!-- Support & Services -->
                    <TableRow class="bg-gray-50">
                      <TableCell class="font-semibold text-gorilla-primary-three" colspan="6">Support & Services</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Support channels</TableCell>
                      <TableCell class="text-center">Forum</TableCell>
                      <TableCell class="text-center">Email</TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5">Email + Phone</TableCell>
                      <TableCell class="text-center">24/7 Phone + Email</TableCell>
                      <TableCell class="text-center">24/7 + Emergency</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Response time</TableCell>
                      <TableCell class="text-center">Community</TableCell>
                      <TableCell class="text-center">48 hours</TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5">24 hours</TableCell>
                      <TableCell class="text-center">2 hours</TableCell>
                      <TableCell class="text-center">1 hour</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Account management</TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>

                    <!-- Security & Compliance -->
                    <TableRow class="bg-gray-50">
                      <TableCell class="font-semibold text-gorilla-primary-three" colspan="6">Security & Compliance</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">SOC 2 Type 2 audit</TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">HIPAA/HITECH compliance</TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Data residency guarantee</TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>

                    <!-- Deployment -->
                    <TableRow class="bg-gray-50">
                      <TableCell class="font-semibold text-gorilla-primary-three" colspan="6">Deployment</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">Cloud hosting (Rwanda)</TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                      <TableCell class="text-center">Optional</TableCell>
                    </TableRow>
                    <TableRow>
                      <TableCell class="font-medium">On-premises option</TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center bg-gorilla-primary/5"><X class="w-5 h-5 text-gray-400 mx-auto" /></TableCell>
                      <TableCell class="text-center">Available</TableCell>
                      <TableCell class="text-center"><CheckCircle class="w-5 h-5 text-gorilla-primary mx-auto" /></TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- FAQ Section -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-4xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Frequently Asked Questions
            </h2>
          </div>

          <div class="space-y-8">
            <Card
              v-for="faq in faqs"
              :key="faq.question"
              class="border border-gray-200 hover:border-gorilla-primary transition-colors duration-200"
            >
              <CardHeader>
                <CardTitle class="text-gorilla-primary-three flex items-start space-x-3">
                  <HelpCircle class="w-6 h-6 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span>{{ faq.question }}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600 leading-relaxed">{{ faq.answer }}</p>
              </CardContent>
            </Card>
          </div>

          <!-- Additional FAQs -->
          <div class="mt-12 space-y-8">
            <Card class="border border-gray-200 hover:border-gorilla-primary transition-colors duration-200">
              <CardHeader>
                <CardTitle class="text-gorilla-primary-three flex items-start space-x-3">
                  <HelpCircle class="w-6 h-6 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span>How does pricing compare to global providers?</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600 leading-relaxed">
                  Ngabo GIS typically costs 40-60% less than Google Maps API and MapBox for equivalent usage in Rwanda,
                  with superior local accuracy and no data egress. Plus, your data stays sovereign. Request a comparison
                  analysis for your specific use case.
                </p>
              </CardContent>
            </Card>

            <Card class="border border-gray-200 hover:border-gorilla-primary transition-colors duration-200">
              <CardHeader>
                <CardTitle class="text-gorilla-primary-three flex items-start space-x-3">
                  <HelpCircle class="w-6 h-6 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span>Can government agencies pay via annual budget allocation?</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600 leading-relaxed">
                  Yes. We align contracts with Rwanda's fiscal year (July 1 - June 30) and accept payment via government
                  treasury processes, purchase orders, and direct budget allocation. Our Government Relations team manages
                  all public sector contracting.
                </p>
              </CardContent>
            </Card>

            <Card class="border border-gray-200 hover:border-gorilla-primary transition-colors duration-200">
              <CardHeader>
                <CardTitle class="text-gorilla-primary-three flex items-start space-x-3">
                  <HelpCircle class="w-6 h-6 text-gorilla-primary mt-0.5 flex-shrink-0" />
                  <span>What's included in the On-Premises deployment?</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600 leading-relaxed">
                  Complete Docker image with all three engines, self-hosted Nominatim and OSRM instances, MySQL database,
                  Redis cache, all dependencies. Includes installation support, configuration assistance, and annual software
                  updates. You provide the hardware infrastructure.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <!-- Trust & Transparency Section -->
      <div class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              No Hidden Fees. No Surprises.
            </h2>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <DollarSign class="w-6 h-6 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">All-Inclusive Pricing</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600">No hidden API charges, no surprise overage fees, no premium feature upcharges</p>
              </CardContent>
            </Card>

            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Activity class="w-6 h-6 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Transparent Rate Limits</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600">Clear documentation of all limits, real-time usage dashboards, proactive notifications</p>
              </CardContent>
            </Card>

            <Card class="text-center p-6">
              <CardHeader>
                <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Shield class="w-6 h-6 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Price Lock Guarantee</CardTitle>
              </CardHeader>
              <CardContent>
                <p class="text-gray-600">Annual contracts lock in pricing, 90-day notice for increases, existing customers grandfathered</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <!-- Call-to-Action Section -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Ready to Build on Rwanda's Geospatial Platform?
            </h2>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Start Immediately -->
            <Card class="text-center p-8">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Zap class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Start Immediately</CardTitle>
                <CardDescription class="mt-2">Get free API access and start building today</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="text-sm text-gray-600">
                    <div class="font-medium">For:</div>
                    <div>Developers, startups, small projects</div>
                  </div>
                  <div class="text-sm text-gray-600">
                    <div class="font-medium">Time to start:</div>
                    <div>2 minutes</div>
                  </div>
                  <Button class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                    Create Free Account
                    <ArrowRight class="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            <!-- Calculate Costs -->
            <Card class="text-center p-8 border-2 border-gorilla-primary">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Calculator class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Calculate Costs</CardTitle>
                <CardDescription class="mt-2">Estimate your usage and see exact pricing</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="text-sm text-gray-600">
                    <div class="font-medium">For:</div>
                    <div>Businesses evaluating options</div>
                  </div>
                  <div class="text-sm text-gray-600">
                    <div class="font-medium">Time to start:</div>
                    <div>5 minutes</div>
                  </div>
                  <Button class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                    Use Pricing Calculator
                    <ArrowRight class="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            <!-- Discuss Requirements -->
            <Card class="text-center p-8">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <MessageSquare class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">Discuss Requirements</CardTitle>
                <CardDescription class="mt-2">Talk to our team about custom solutions</CardDescription>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="text-sm text-gray-600">
                    <div class="font-medium">For:</div>
                    <div>Enterprises, government, complex needs</div>
                  </div>
                  <div class="text-sm text-gray-600">
                    <div class="font-medium">Time to start:</div>
                    <div>24 hours</div>
                  </div>
                  <Button class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                    Schedule Consultation
                    <ArrowRight class="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Trust Footer -->
          <div class="mt-16 text-center">
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 max-w-3xl mx-auto">
              <div class="text-center">
                <div class="text-lg font-semibold text-gorilla-primary">6,000+ users</div>
                <div class="text-sm text-gray-600">Trusted by</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gorilla-primary">99.9% uptime</div>
                <div class="text-sm text-gray-600">SLA guarantee</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gorilla-primary">Built in Rwanda</div>
                <div class="text-sm text-gray-600">Hosted locally</div>
              </div>
              <div class="text-center">
                <div class="text-lg font-semibold text-gorilla-primary">No credit card</div>
                <div class="text-sm text-gray-600">Required to start</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
