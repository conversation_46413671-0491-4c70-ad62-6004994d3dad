<script setup>
import { Head } from "@inertiajs/vue3";
import AppLayout from "@/Layouts/AppLayout.vue";
import { ref } from 'vue';
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  MapPin,
  Map,
  Search,
  BarChart3,
  Navigation,
  Zap,
  Shield,
  Globe,
  Server,
  CheckCircle,
  ArrowRight,
  Users,
  Clock,
  Target,
  Database,
  Cpu,
  Lock,
  Building,
  Hospital,
  Tractor,
  Truck,
  GraduationCap,
  Camera,
  Home,
  Smartphone,
  Wrench,
  Copy,
  Eye,
  Settings,
  ChevronRight,
  Activity,
  TrendingUp,
  Layers
} from 'lucide-vue-next';

// Reactive data
const activeEngine = ref('indexing');
const metrics = ref({
  users: '6,000+',
  accuracy: '99.7%',
  queries: '1M+',
  since: 'Q2 2025'
});

const engines = [
  { id: 'indexing', name: 'Indexing Engine', icon: Search },
  { id: 'data', name: 'Data Engine', icon: Database },
  { id: 'travel', name: 'Travel Engine', icon: Navigation }
];

const sectors = [
  { name: 'Health', icon: Hospital, metric: '40% faster response times', description: 'Optimize ambulance dispatch and medical supply chains' },
  { name: 'Agriculture', icon: Tractor, metric: '30% reduction in transport costs', description: 'Efficient farm-to-market logistics and yield optimization' },
  { name: 'Logistics', icon: Truck, metric: '50% faster deliveries', description: 'Last-mile delivery optimization with real-time tracking' },
  { name: 'Government', icon: Building, metric: '60% increase in service awareness', description: 'Enhanced citizen service access and resource allocation' },
  { name: 'Mining', icon: Wrench, metric: 'Resource optimization', description: 'Resource mapping and supply chain coordination' },
  { name: 'Education', icon: GraduationCap, metric: 'Route optimization', description: 'School location optimization and student transportation' },
  { name: 'Tourism', icon: Camera, metric: 'Tour optimization', description: 'Attraction mapping and tour route optimization' },
  { name: 'Real Estate', icon: Home, metric: 'Market analysis', description: 'Property location intelligence and market analysis' },
  { name: 'Fintech', icon: Smartphone, metric: 'Network optimization', description: 'Branch network optimization and agent location planning' },
  { name: 'Utilities', icon: Zap, metric: 'Coverage analysis', description: 'Infrastructure mapping and service coverage analysis' }
];

const trustMetrics = [
  { icon: Target, value: '99.7%', label: 'Query Accuracy' },
  { icon: Activity, value: '99.9%', label: 'Uptime (Enterprise)' },
  { icon: TrendingUp, value: '1M+', label: 'Queries/Second Capacity' },
  { icon: Clock, value: '<50ms', label: 'Average Response Time' },
  { icon: Layers, value: '6', label: 'Administrative Levels Covered' }
];
</script>

<template>
  <Head title="Product - Ngabo GIS" />
  <AppLayout>
    <div class="min-h-screen bg-cta-background-two">
      <!-- Hero Section -->
      <div class="bg-white border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="py-16 lg:py-24">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <!-- Hero Content -->
              <div>
                <h1 class="text-4xl lg:text-6xl font-bold text-gorilla-primary-three leading-tight">
                  Rwanda's Geospatial Intelligence Platform
                </h1>
                <p class="mt-6 text-xl text-gray-600 leading-relaxed">
                  Three proprietary engines processing millions of queries per second.
                  Proven accuracy. Complete sovereignty. Built for Rwanda by Rwandans.
                </p>

                <!-- Key Metrics -->
                <div class="mt-8 grid grid-cols-2 lg:grid-cols-4 gap-6">
                  <div class="text-center">
                    <div class="text-2xl font-bold text-gorilla-primary">{{ metrics.users }}</div>
                    <div class="text-sm text-gray-600">Active Users</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-gorilla-primary">{{ metrics.accuracy }}</div>
                    <div class="text-sm text-gray-600">Query Accuracy</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-gorilla-primary">{{ metrics.queries }}</div>
                    <div class="text-sm text-gray-600">Queries/Second</div>
                  </div>
                  <div class="text-center">
                    <div class="text-2xl font-bold text-gorilla-primary">{{ metrics.since }}</div>
                    <div class="text-sm text-gray-600">Since</div>
                  </div>
                </div>

                <!-- CTAs -->
                <div class="mt-10 flex flex-col sm:flex-row gap-4">
                  <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white px-8 py-3 text-lg">
                    Start Building Now
                    <ArrowRight class="ml-2 h-5 w-5" />
                  </Button>
                  <Button variant="outline" class="border-gorilla-primary text-gorilla-primary hover:bg-gorilla-primary hover:text-white px-8 py-3 text-lg">
                    Schedule Enterprise Demo
                  </Button>
                </div>
              </div>

              <!-- Hero Visual -->
              <div class="relative">
                <div class="bg-gradient-to-br from-gorilla-primary/10 to-gorilla-primary-two/10 rounded-2xl p-8 h-96 flex items-center justify-center">
                  <div class="text-center">
                    <div class="w-24 h-24 bg-gorilla-primary rounded-full flex items-center justify-center mx-auto mb-4">
                      <Map class="w-12 h-12 text-white" />
                    </div>
                    <div class="text-gorilla-primary-three font-semibold">Interactive Rwanda Map</div>
                    <div class="text-sm text-gray-600 mt-2">Real-time data layers • Live queries • Route visualization</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Platform Overview Section -->
      <div class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Three Engines. Infinite Applications.
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              Ngabo GIS integrates search, data fusion, and optimization into a unified platform,
              powered by Cognitive Routing and proprietary pattern matching algorithms.
            </p>
          </div>

          <!-- Engine Navigation -->
          <div class="flex justify-center mb-12">
            <div class="bg-white border border-gray-200 rounded-full p-2 inline-flex space-x-1">
              <button
                v-for="engine in engines"
                :key="engine.id"
                @click="activeEngine = engine.id"
                :class="[
                  'px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 flex items-center space-x-2',
                  activeEngine === engine.id
                    ? 'bg-gorilla-primary text-white'
                    : 'text-gorilla-primary-three hover:bg-cta-background-one'
                ]"
              >
                <component :is="engine.icon" class="w-4 h-4" />
                <span>{{ engine.name }}</span>
              </button>
            </div>
          </div>

          <!-- Engine Details -->
          <div class="space-y-16">
            <!-- Indexing Engine -->
            <div v-show="activeEngine === 'indexing'" class="space-y-8">
              <Card>
                <CardHeader>
                  <div class="flex items-center space-x-3 mb-4">
                    <div class="w-12 h-12 bg-gorilla-primary rounded-xl flex items-center justify-center">
                      <Search class="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gorilla-primary bg-gorilla-primary/10 px-3 py-1 rounded-full inline-block mb-2">
                        Search & Geocoding
                      </div>
                      <CardTitle class="text-2xl text-gorilla-primary-three">Hyper-Local Intelligence for Every Query</CardTitle>
                    </div>
                  </div>
                  <CardDescription class="text-lg">
                    While global providers struggle with Rwandan addresses, our Indexing Engine delivers 99.7% accuracy
                    through proprietary pattern recognition that understands Rwanda's six-level administrative structure.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Core Capabilities -->
                    <div class="space-y-6">
                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Intelligent Pattern Recognition</h4>
                        <ul class="space-y-2 text-gray-600">
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>[text, text] → District identification</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>[text, text, text] → Sector resolution</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>[text, text, text, text] → Cell precision</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>Patent-protected algorithms (RW/2025/002)</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Performance</h4>
                        <ul class="space-y-2 text-gray-600">
                          <li class="flex items-start space-x-2">
                            <Clock class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>Sub-50ms response times</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <Zap class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>Microsecond retrieval for cached queries</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <Database class="w-4 h-4 text-gorilla-primary mt-1 flex-shrink-0" />
                            <span>Laravel Cognitive Caching architecture</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <!-- Real-World Example -->
                    <div class="bg-cta-background-one rounded-xl p-6">
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Real-World Example</h4>
                      <div class="space-y-3">
                        <div class="bg-white rounded-lg p-4">
                          <div class="text-sm font-medium text-gray-700 mb-2">Query:</div>
                          <div class="font-mono text-gorilla-primary">"pharmacy Nyabugogo"</div>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <div class="font-medium text-gray-700">Processing Time:</div>
                            <div class="text-gorilla-primary font-semibold">42ms</div>
                          </div>
                          <div>
                            <div class="font-medium text-gray-700">Accuracy:</div>
                            <div class="text-gorilla-primary font-semibold">99.7%</div>
                          </div>
                        </div>
                        <div class="text-sm text-gray-600">
                          Result: Ranked list of 12 pharmacies with addresses, coordinates, hours
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Data Engine -->
            <div v-show="activeEngine === 'data'" class="space-y-8">
              <Card>
                <CardHeader>
                  <div class="flex items-center space-x-3 mb-4">
                    <div class="w-12 h-12 bg-gorilla-primary-two rounded-xl flex items-center justify-center">
                      <Database class="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gorilla-primary-two bg-gorilla-primary-two/10 px-3 py-1 rounded-full inline-block mb-2">
                        Custom Mapping & Analytics
                      </div>
                      <CardTitle class="text-2xl text-gorilla-primary-three">Transform Location Data into Business Intelligence</CardTitle>
                    </div>
                  </div>
                  <CardDescription class="text-lg">
                    Beyond basic mapping, the Data Engine fuses multiple data sources, enables collaborative editing,
                    and generates advanced spatial analytics—all while maintaining complete data sovereignty.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Core Capabilities -->
                    <div class="space-y-6">
                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Custom Map Creation</h4>
                        <ul class="space-y-2 text-gray-600">
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-two mt-1 flex-shrink-0" />
                            <span>GeoJSON-native data model (Point, Line, Polygon)</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-two mt-1 flex-shrink-0" />
                            <span>Schema-on-read for flexible data structures</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-two mt-1 flex-shrink-0" />
                            <span>Multi-user collaboration with CRDT</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Advanced Analytics Suite</h4>
                        <ul class="space-y-2 text-gray-600">
                          <li class="flex items-start space-x-2">
                            <BarChart3 class="w-4 h-4 text-gorilla-primary-two mt-1 flex-shrink-0" />
                            <span>Heatmap Analysis for density visualization</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <Map class="w-4 h-4 text-gorilla-primary-two mt-1 flex-shrink-0" />
                            <span>Choropleth Mapping for area comparison</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <Target class="w-4 h-4 text-gorilla-primary-two mt-1 flex-shrink-0" />
                            <span>K-nearest neighbors and spatial queries</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <!-- Workflow Example -->
                    <div class="bg-cta-background-one rounded-xl p-6">
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Workflow Example</h4>
                      <div class="space-y-3">
                        <div class="text-sm font-medium text-gorilla-primary-two">Pharmaceutical Distribution Optimization</div>
                        <div class="space-y-2 text-sm text-gray-600">
                          <div class="flex items-start space-x-2">
                            <div class="w-5 h-5 bg-gorilla-primary-two text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">1</div>
                            <span>Data Engine ingests inventory CSV (1,200 pharmacies)</span>
                          </div>
                          <div class="flex items-start space-x-2">
                            <div class="w-5 h-5 bg-gorilla-primary-two text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">2</div>
                            <span>Auto-geocoding resolves addresses (99.7% success)</span>
                          </div>
                          <div class="flex items-start space-x-2">
                            <div class="w-5 h-5 bg-gorilla-primary-two text-white rounded-full flex items-center justify-center text-xs font-bold mt-0.5">3</div>
                            <span>Choropleth map shows availability by district</span>
                          </div>
                        </div>
                        <div class="bg-white rounded-lg p-3 mt-4">
                          <div class="text-sm font-medium text-gray-700">Outcome:</div>
                          <div class="text-gorilla-primary-two font-semibold">35% improvement in distribution efficiency</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Travel Engine -->
            <div v-show="activeEngine === 'travel'" class="space-y-8">
              <Card>
                <CardHeader>
                  <div class="flex items-center space-x-3 mb-4">
                    <div class="w-12 h-12 bg-gorilla-primary-three rounded-xl flex items-center justify-center">
                      <Navigation class="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gorilla-primary-three bg-gorilla-primary-three/10 px-3 py-1 rounded-full inline-block mb-2">
                        Routing & Optimization
                      </div>
                      <CardTitle class="text-2xl text-gorilla-primary-three">Cognitive Routing for Complex Logistics</CardTitle>
                    </div>
                  </div>
                  <CardDescription class="text-lg">
                    From simple navigation to multi-stop vehicle routing problems, the Travel Engine computes optimal routes
                    in under 3 seconds—even for 50-waypoint scenarios with capacity constraints and time windows.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Core Capabilities -->
                    <div class="space-y-6">
                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Advanced Route Optimization</h4>
                        <ul class="space-y-2 text-gray-600">
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Traveling Salesman Problem (TSP) solving</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Vehicle Routing Problem (VRP) with constraints</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Support for up to 150 waypoints</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <CheckCircle class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Sub-3-second computation for 50-stop routes</span>
                          </li>
                        </ul>
                      </div>

                      <div>
                        <h4 class="font-semibold text-gorilla-primary-three mb-3">Real-Time Geofencing</h4>
                        <ul class="space-y-2 text-gray-600">
                          <li class="flex items-start space-x-2">
                            <MapPin class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Administrative boundary monitoring (all 6 levels)</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <Shield class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Custom polygon geofences</span>
                          </li>
                          <li class="flex items-start space-x-2">
                            <Activity class="w-4 h-4 text-gorilla-primary-three mt-1 flex-shrink-0" />
                            <span>Moving asset tracking via WebSocket</span>
                          </li>
                        </ul>
                      </div>
                    </div>

                    <!-- Complete Workflow Example -->
                    <div class="bg-cta-background-one rounded-xl p-6">
                      <h4 class="font-semibold text-gorilla-primary-three mb-4">Complete Workflow Example</h4>
                      <div class="space-y-3">
                        <div class="text-sm font-medium text-gorilla-primary-three">Medical Supply Distribution in Kigali</div>
                        <div class="bg-white rounded-lg p-3">
                          <div class="text-sm font-medium text-gray-700 mb-2">Query:</div>
                          <div class="font-mono text-sm text-gorilla-primary-three">"Find paracetamol &lt;5,000 RWF near Nyabugogo, plan delivery route"</div>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600">
                          <div>1. Indexing Engine resolves location and finds 8 pharmacies</div>
                          <div>2. Data Engine filters by stock and price (5 pharmacies match)</div>
                          <div>3. Travel Engine computes optimal TSP route</div>
                        </div>
                        <div class="bg-white rounded-lg p-3 mt-4">
                          <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <div class="font-medium text-gray-700">Total Distance:</div>
                              <div class="text-gorilla-primary-three font-semibold">8.2 km</div>
                            </div>
                            <div>
                              <div class="font-medium text-gray-700">Estimated Time:</div>
                              <div class="text-gorilla-primary-three font-semibold">22 minutes</div>
                            </div>
                          </div>
                          <div class="text-sm text-gray-600 mt-2">
                            Result: 45% faster than manual planning, 30% fuel savings
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>

      <!-- Technical Architecture Section -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Enterprise-Grade Infrastructure Built for Scale
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              Millions of queries per second with microsecond latency. This is the technology powering Rwanda's location intelligence.
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Technology Stack -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                  <Cpu class="w-6 h-6" />
                  <span>Technology Stack</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="grid grid-cols-2 gap-4">
                    <div>
                      <div class="text-sm font-medium text-gray-700">Backend</div>
                      <div class="text-gorilla-primary-three font-semibold">Laravel 12 / PHP 8.4</div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-700">Database</div>
                      <div class="text-gorilla-primary-three font-semibold">MySQL 8.0 + Spatial</div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-700">Caching</div>
                      <div class="text-gorilla-primary-three font-semibold">Redis + Cognitive Caching</div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-700">Frontend</div>
                      <div class="text-gorilla-primary-three font-semibold">Vue 3 / TypeScript</div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-700">Search</div>
                      <div class="text-gorilla-primary-three font-semibold">TypeSense</div>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-700">Deployment</div>
                      <div class="text-gorilla-primary-three font-semibold">Docker + Kubernetes</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <!-- Performance Metrics -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                  <Activity class="w-6 h-6" />
                  <span>Performance Metrics</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div class="space-y-4">
                  <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">Throughput</span>
                    <span class="font-semibold text-gorilla-primary">1,000,000+ queries/second</span>
                  </div>
                  <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">Response Time</span>
                    <span class="font-semibold text-gorilla-primary">&lt;50ms for Indexing</span>
                  </div>
                  <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">TSP Optimization</span>
                    <span class="font-semibold text-gorilla-primary">&lt;3s for 50 waypoints</span>
                  </div>
                  <div class="flex items-center justify-between py-2 border-b border-gray-100">
                    <span class="text-gray-600">Cache Retrieval</span>
                    <span class="font-semibold text-gorilla-primary">Microsecond latency</span>
                  </div>
                  <div class="flex items-center justify-between py-2">
                    <span class="text-gray-600">Uptime SLA</span>
                    <span class="font-semibold text-gorilla-primary">99.9% (Enterprise)</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- Integration Capabilities -->
          <Card class="mt-12">
            <CardHeader>
              <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                <Globe class="w-6 h-6" />
                <span>Integration Capabilities</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="text-center">
                  <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Globe class="w-6 h-6 text-gorilla-primary" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three">RESTful APIs</div>
                  <div class="text-sm text-gray-600">OpenAPI documented</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Smartphone class="w-6 h-6 text-gorilla-primary" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three">Mobile SDKs</div>
                  <div class="text-sm text-gray-600">iOS, Android, Web</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Database class="w-6 h-6 text-gorilla-primary" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three">Data Formats</div>
                  <div class="text-sm text-gray-600">GeoJSON, GPX, CSV, PDF</div>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-3">
                    <Activity class="w-6 h-6 text-gorilla-primary" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three">Real-time</div>
                  <div class="text-sm text-gray-600">WebSocket, gRPC</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Data Sovereignty Section -->
      <div class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Your Data Stays in Rwanda
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              Complete data sovereignty with enterprise-grade security and compliance. No foreign servers. No data egress. Total control.
            </p>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Data Sovereignty -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                  <Shield class="w-6 h-6" />
                  <span>Data Sovereignty Guaranteed</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-3">
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">All data processed and stored within Rwanda</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">No transmission to foreign servers</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">Rwandan-owned and operated infrastructure</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">Alignment with Rwanda Data Protection Law</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">Government and enterprise-approved</span>
                  </li>
                </ul>
              </CardContent>
            </Card>

            <!-- Security & Compliance -->
            <Card>
              <CardHeader>
                <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                  <Lock class="w-6 h-6" />
                  <span>Security & Compliance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-3">
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">Encryption at rest (AES-256)</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">Encryption in transit (TLS 1.3)</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">SOC 2 Type 2 audited (Enterprise tier)</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">HIPAA/HITECH compliant (Enterprise tier)</span>
                  </li>
                  <li class="flex items-start space-x-3">
                    <CheckCircle class="w-5 h-5 text-gorilla-primary mt-0.5 flex-shrink-0" />
                    <span class="text-gray-600">SAML SSO support (Enterprise tier)</span>
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>

          <!-- Deployment Options -->
          <Card class="mt-12">
            <CardHeader>
              <CardTitle class="text-gorilla-primary flex items-center space-x-2">
                <Server class="w-6 h-6" />
                <span>Deployment Flexibility</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center p-6 bg-cta-background-one rounded-xl">
                  <div class="w-12 h-12 bg-gorilla-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Globe class="w-6 h-6 text-white" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three mb-2">Cloud Hosting</div>
                  <div class="text-sm text-gray-600">TKD-managed infrastructure in Rwanda</div>
                </div>
                <div class="text-center p-6 bg-cta-background-one rounded-xl">
                  <div class="w-12 h-12 bg-gorilla-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Server class="w-6 h-6 text-white" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three mb-2">On-Premises</div>
                  <div class="text-sm text-gray-600">Docker images for complete isolation</div>
                </div>
                <div class="text-center p-6 bg-cta-background-one rounded-xl">
                  <div class="w-12 h-12 bg-gorilla-primary rounded-xl flex items-center justify-center mx-auto mb-4">
                    <Layers class="w-6 h-6 text-white" />
                  </div>
                  <div class="font-semibold text-gorilla-primary-three mb-2">Hybrid</div>
                  <div class="text-sm text-gray-600">Mix cloud and on-premises components</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Sector Applications Section -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Proven Across 10 Critical Sectors
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
              From healthcare to agriculture, Ngabo GIS powers location intelligence for Rwanda's most important industries.
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
            <div
              v-for="sector in sectors"
              :key="sector.name"
              class="group p-6 bg-white border border-gray-200 rounded-xl hover:border-gorilla-primary hover:shadow-lg transition-all duration-200 cursor-pointer"
            >
              <div class="text-center">
                <div class="w-12 h-12 bg-gorilla-primary/10 group-hover:bg-gorilla-primary rounded-xl flex items-center justify-center mx-auto mb-4 transition-colors duration-200">
                  <component :is="sector.icon" class="w-6 h-6 text-gorilla-primary group-hover:text-white transition-colors duration-200" />
                </div>
                <div class="font-semibold text-gorilla-primary-three mb-2">{{ sector.name }}</div>
                <div class="text-sm text-gray-600 mb-3">{{ sector.description }}</div>
                <div class="text-sm font-medium text-gorilla-primary">{{ sector.metric }}</div>
                <div class="mt-4 text-sm text-gorilla-primary group-hover:text-gorilla-primary-three transition-colors duration-200 flex items-center justify-center">
                  Explore Use Cases
                  <ArrowRight class="ml-1 w-4 h-4" />
                </div>
              </div>
            </div>
          </div>

          <div class="text-center mt-12">
            <Button class="bg-gorilla-primary hover:bg-gorilla-primary/90 text-white px-8 py-3">
              View All Sector Solutions
              <ArrowRight class="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      <!-- Customer Trust Section -->
      <div class="py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Trusted by Rwanda's Leading Organizations
            </h2>
            <div class="inline-flex items-center space-x-2 bg-gorilla-primary/10 px-6 py-3 rounded-full">
              <Users class="w-5 h-5 text-gorilla-primary" />
              <span class="font-semibold text-gorilla-primary">6,000+ Active Users Since Q2 2025</span>
            </div>
          </div>

          <!-- Trust Metrics -->
          <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-6 mb-16">
            <div
              v-for="metric in trustMetrics"
              :key="metric.label"
              class="text-center p-6 bg-white rounded-xl border border-gray-200"
            >
              <div class="w-12 h-12 bg-gorilla-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <component :is="metric.icon" class="w-6 h-6 text-gorilla-primary" />
              </div>
              <div class="text-2xl font-bold text-gorilla-primary mb-1">{{ metric.value }}</div>
              <div class="text-sm text-gray-600">{{ metric.label }}</div>
            </div>
          </div>

          <!-- Customer Categories -->
          <div class="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div class="text-center p-6 bg-cta-background-one rounded-xl">
              <Building class="w-8 h-8 text-gorilla-primary mx-auto mb-3" />
              <div class="font-semibold text-gorilla-primary-three">Government Agencies</div>
            </div>
            <div class="text-center p-6 bg-cta-background-one rounded-xl">
              <Building class="w-8 h-8 text-gorilla-primary mx-auto mb-3" />
              <div class="font-semibold text-gorilla-primary-three">Enterprise Organizations</div>
            </div>
            <div class="text-center p-6 bg-cta-background-one rounded-xl">
              <Users class="w-8 h-8 text-gorilla-primary mx-auto mb-3" />
              <div class="font-semibold text-gorilla-primary-three">NGOs & Development Partners</div>
            </div>
            <div class="text-center p-6 bg-cta-background-one rounded-xl">
              <Building class="w-8 h-8 text-gorilla-primary mx-auto mb-3" />
              <div class="font-semibold text-gorilla-primary-three">Small & Medium Businesses</div>
            </div>
            <div class="text-center p-6 bg-cta-background-one rounded-xl">
              <Users class="w-8 h-8 text-gorilla-primary mx-auto mb-3" />
              <div class="font-semibold text-gorilla-primary-three">Individual Developers</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Call-to-Action Section -->
      <div class="bg-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-6 lg:px-8">
          <div class="text-center mb-16">
            <h2 class="text-3xl lg:text-4xl font-bold text-gorilla-primary-three mb-4">
              Start Building on Rwanda's Geospatial Platform
            </h2>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- For Developers -->
            <Card class="text-center p-8">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Users class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">For Developers</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-gray-600 mb-6">
                  <li>Free API access to get started</li>
                  <li>100 requests/day, 50/minute</li>
                  <li>Full documentation and SDKs</li>
                </ul>
                <Button class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                  Get API Key
                </Button>
              </CardContent>
            </Card>

            <!-- For Businesses -->
            <Card class="text-center p-8 border-2 border-gorilla-primary">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Building class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">For Businesses</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-gray-600 mb-6">
                  <li>Flexible pay-as-you-go pricing</li>
                  <li>Scale from 1,000 to millions of queries</li>
                  <li>No upfront commitment</li>
                </ul>
                <Button class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                  Calculate Your Costs
                </Button>
              </CardContent>
            </Card>

            <!-- For Enterprises -->
            <Card class="text-center p-8">
              <CardHeader>
                <div class="w-16 h-16 bg-gorilla-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Shield class="w-8 h-8 text-gorilla-primary" />
                </div>
                <CardTitle class="text-gorilla-primary-three">For Enterprises</CardTitle>
              </CardHeader>
              <CardContent>
                <ul class="space-y-2 text-gray-600 mb-6">
                  <li>Dedicated infrastructure and support</li>
                  <li>SOC 2, HIPAA compliance</li>
                  <li>On-premises deployment available</li>
                </ul>
                <Button class="w-full bg-gorilla-primary hover:bg-gorilla-primary/90 text-white">
                  Schedule Enterprise Demo
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  </AppLayout>
</template>
