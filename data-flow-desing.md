Software Requirements Specification (SRS): Ngabo GIS
Document Control
Version: 1.0
Date: September 2, 2025
Author: HIRWA DEVELOPER
Status: Draft for Review
Copyright Notice: © 2025 Ngabo GIS Ltd. All rights reserved. This document contains proprietary information protected under international copyright laws. Unauthorized reproduction, distribution, or disclosure is strictly prohibited. Intellectual property rights in the Ngabo GIS Engines and associated algorithms are exclusively owned by TKD, with patents granted for integrated geospatial processing methods (e.g., hyper-local pattern matching and multi-engine workflow orchestration).

TKD Ltd., headquartered in Kigali, is Rwanda's software development firm, delivering a fully operational, sovereign mapping ecosystem since Q2 2025. Built by Rwandan engineers, the platform leverages localized datasets and proprietary innovations to supplant global dependencies like Google Maps and MapBox. With over 6,000 active users and integrations across government and enterprise sectors, Ngabo GIS is the definitive backbone for location-based services, ensuring data sovereignty, operational resilience, and unparalleled accuracy tailored to Rwanda's topographic, administrative, and socioeconomic landscape.
1. Introduction
1.1 Purpose
This Software Requirements Specification (SRS) defines the functional and non-functional requirements for the Ngabo GIS application, Rwanda's mainstream geospatial ecosystem. It serves as a technical blueprint for development, testing, and deployment, aligning with the Business Requirements Document (BRD) while detailing implementation specifics. The SRS targets developers, testers, stakeholders, and maintainers, ensuring the system meets Rwanda’s digital sovereignty goals.
1.2 Scope
Ngabo GIS is a web and mobile-accessible geospatial platform powered by three proprietary engines: Indexing (search/geocoding), Data (custom mapping/analytics), and Travel (routing/optimization). It integrates third-party services (Nominatim for public indexing, OSRM for routing) with a bespoke Laravel Cognitive Caching architecture, utilizing MySQL, Docker, TypeSense, Laravel, Redis, and RoadRunner to process millions of queries per second with microsecond latency. The platform supports 10 key sectors, delivering tailored workflows for health, agriculture, and more, with a tiered business model for flexible adoption.
Inclusions:
RESTful APIs for search, data fusion, and TSP/VRP solving with rate limits.
User dashboards for visualization and management.
Sector-specific modules with hyper-local accuracy.
Flexible pricing tiers for developers, self-serve users, enterprises, and on-premises deployments.
Exclusions:
Hardware procurement (assumes cloud/on-prem Dockerized deployment).
Third-party data sourcing (relies on localized feeds).
1.3 Definitions, Acronyms, and Abbreviations
TKD :  Trusted Kigali Developers LTD
TSP: Travelling Salesman Problem – Multi-stop route optimization.
VRP: Vehicle Routing Problem – Capacitated TSP variant.
POI: Point of Interest – Geospatial data points (e.g., pharmacies).
GeoJSON: Standard format for geographic data structures (RFC 7946).
Nominatim: Open-source geocoding service.
OSRM: Open Source Routing Machine for routing calculations.
MySQL: Relational database management system with spatial extensions.
CRDT: Conflict-Free Replicated Data Type – For collaborative data handling.
API: Application Programming Interface.
SLA: Service Level Agreement.
UHC: Universal Health Coverage.
SOC 2: Service Organization Control 2 – Security and compliance standard.
HIPAA/HITECH: US health data privacy standards (relevant for global compliance).
1.4 References
Business Requirements Document (BRD): Ngabo GIS, Version 3.0, June 20, 2025.
1.5 Overview
Section 2 outlines the product perspective, functions, and business model. Section 3 details functional, non-functional, and interface requirements. Section 4 covers supporting information, including assumptions and appendices.
2. Overall Description
2.1 Product Perspective
Ngabo GIS addresses Rwanda’s need for a sovereign geospatial ecosystem, replacing global providers with a localized solution. It integrates third-party tools (Nominatim, OSRM) with proprietary enhancements, leveraging Rwanda-specific datasets for 99.7% query accuracy. The platform’s Laravel Cognitive Caching architecture ensures high-performance, Dockerized deployments, aligning with Rwanda’s digital economy goals. A tiered business model supports diverse users, from developers to enterprises, with flexible pricing and payment options for government and NGOs.
2.2 Product Functions
User Account Management: Account creation, login, password reset, profile updates, and session deletion.
Search and Geocoding: Hyper-local query resolution using text or lat/long, with pattern-based identification of administrative levels.
Custom Maps: Creation, editing, sharing of maps with custom fields, places, and polygons; geofencing for moving items.
Data Management: Multi-map data support, custom fields, pagination, and analytics integration.
Routing and Optimization: Geofencing calculations, navigation with multi-waypoints, and TSP/VRP solving.
Visualization: Interactive maps with real-time tracking and export capabilities.
Sectoral Integrations: Tailored modules for health (e.g., ambulance dispatch), agriculture (e.g., farm-to-market VRP), and more.
Administration: User management, API rate limiting, and usage dashboards.
2.3 Business Model
Ngabo GIS offers a tiered pricing model to cater to diverse users, ensuring accessibility and scalability while supporting Rwanda’s digital economy. The tiers are designed to address the needs of developers, small teams, high-volume users, enterprises, and on-premises deployments, with special considerations for government and NGO payments.
Developer Tier:


Access: Free API access with limits of 100 requests/day and 50 requests/minute.
Target Users: Individual developers, startups, and small projects testing integration.
Features: Basic search, geocoding, and routing APIs; access to public datasets.
Purpose: Encourages adoption and prototyping with minimal barriers.
Self-Serve Pay-As-You-Go:


Pricing: 1,000 lookups/month free, then $0.10 per 1,000 lookups.
Billing: Usage rolled up and charged on the 1st of the following month.
Target Users: Small to medium businesses, freelance developers, and ad-hoc projects.
Features: Full API access, custom map creation, basic geofencing (paid add-on).
Tools: Pricing calculator for cost estimation.
Purpose: Flexible and affordable for most projects, with no upfront commitment.
Self-Serve Unlimited:


Pricing: Starts at $1,000/month or $11,400/year (5% annual discount).
Performance: Supports 3,000 lookups/minute per instance.
Target Users: High-volume SMEs, logistics firms, and tech startups.
Features: Dedicated instance, full API access, advanced geofencing, analytics (heatmaps, choropleths).
Purpose: Built for high-throughput geocoding and routing needs.
Enterprise Unlimited:


Pricing: Starts at $2,500/month or $30,000/year.
Compliance: SOC 2 Type 2 audited, HIPAA/HITECH compliant, 99.9% uptime SLA, SAML SSO, data residency in Rwanda.
Target Users: Large enterprises, government agencies, and NGOs with strict security needs.
Features: All Self-Serve Unlimited features, plus enhanced security and compliance.
Purpose: Meets stringent regulatory and operational requirements for sensitive data.
On-Premises:


Pricing: Starts at $30,000/year per instance, plus $15,000/year per additional instance.
Deployment: Geocodio API as a Docker image, private and isolated, no internet required.
Target Users: Organizations requiring full control over infrastructure (e.g., government, critical utilities).
Features: Full platform functionality in a self-hosted environment.
Purpose: Ensures data sovereignty and isolation for high-security use cases.
Government and NGO Payment Model:


Payment Flexibility: Government agencies and registered NGOs can pay via:
Bank Transfer: Direct payments to Ngabo GIS’ Rwandan bank account, with invoicing in RWF or USD.
Purchase Orders: Formal procurement processes with 30-60 day payment terms, subject to credit approval.
Grants/Subsidies: Support for grant-funded NGOs, with negotiated discounts for public-good projects (e.g., health, education).
Annual Contracts: Fixed-rate agreements for predictable budgeting, aligned with fiscal cycles.
Dedicated Support: Priority onboarding, custom integrations, and account management for government/NGO clients.
Purpose: Facilitates adoption by public sector and non-profits, aligning with Rwanda’s development goals.
2.4 User Classes and Characteristics
Citizens/Individuals: Access free search via web/mobile; require intuitive UI.
Businesses/SMEs: Use Pay-As-You-Go or Self-Serve Unlimited tiers for bulk geocoding, tracking; need APIs, analytics.
Enterprises/Government/NGOs: Use Enterprise Unlimited or On-Premises tiers; require advanced analytics, compliance, SLAs.
Developers: Use Developer tier; consume APIs; expect robust SDKs, documentation.
Administrators: Manage POIs, users, and system health; require secure access.
2.5 Operating Environment
Hardware: Cloud or on-prem; min 16GB RAM/node.
Software: Backend: Laravel 12/PHP 8.4, MySQL 8.0 with spatial extensions, Redis, RoadRunner. Frontend: TypeScript/Vue 3. Mobile: SDK for iOS/Android. Deployment: Docker.
Network: High-bandwidth for telemetry; HTTPS mandatory.
Compatibility: Browsers (Chrome/Firefox latest), OS (Linux for dev).
2.6 Design and Implementation Constraints
Database: MySQL 8.0 with spatial indexes (no PostGIS).
Third-Party Services: Nominatim (geocoding), OSRM (routing), hosted on-prem.
Containerization: Docker for all services.
Language: PHP (backend), VueJs(frontend).
Standards: RESTful APIs, GeoJSON outputs, OIDC auth.
Testing Framework: Pest for unit and integration testing.
2.7 Assumptions and Dependencies
Assumptions: Stable localized data feeds; Dockerized infra available; internet for cloud users.
Dependencies: Nominatim/OSRM (on-prem), Stripe (billing); Redis/RoadRunner for performance.
3. Specific Requirements
3.1 External Interfaces
3.1.1 User Interfaces
Web Dashboard: Vue 3 SPA with MapLibre GL for interactive maps; responsive, tab-based navigation; supports account management, map creation, and analytics.
Mobile SDK: Embeddable components for search, tracking, routing; supports iOS/Android.
API Endpoints: REST (e.g., POST /tsp/optimize); OpenAPI-documented; rate-limited per tier (100/day, 50/min for Developer tier).
3.1.2 Hardware Interfaces
GPS Integration: Device APIs for real-time location pings.
3.1.3 Software Interfaces
Nominatim: For forward/reverse geocoding; Rwanda-specific instance.
OSRM: For routing calculations; customized with Rwanda road graphs.
Notifications: Twilio SMS, Ably WebSockets for alerts.
Auth: Keycloak OIDC for RBAC; SAML SSO for Enterprise tier.
Billing: Stripe for Pay-As-You-Go and subscription payments; manual invoicing for government/NGOs.
3.1.4 Communication Interfaces
Protocols: HTTPS (REST APIs), WebSockets (live tracking), gRPC (engine-to-engine).
3.2 Functional Requirements
3.2.1 User Account Management
FR-AM-01: Users shall create an account via email/password or social login, with email verification.
FR-AM-02: Users shall login using credentials, with two-factor authentication option.
FR-AM-03: Users shall reset passwords via email link, with secure token expiration (24 hours).
FR-AM-04: Users shall update profiles (e.g., name, email, avatar) with validation.
FR-AM-05: Users shall delete browser sessions, logging out from all devices.
3.2.2 Ngabo GIS Indexing Engine
FR-IE-01: Process text-based searches to identify administrative levels (province, district, sector, cell, village, health facility) and patterns locally without third-party services.
Pattern Recognition: Detect [text, text] as district; [text,text,text] as sector; [text,text,text,text] as cell; [text,text,text,text,text] as village; [text,text,text,text,text,text] as place.
Input: Query string.
Output: Ranked GeoJSON results in <50ms.
FR-IE-02: Support latitude/longitude-based reverse searches, returning nearest administrative entities.
FR-IE-03: Support bulk geocoding API (up to 10K queries/call) with rate limiting (100/day, 50/min for Developer tier).
FR-IE-04: Integrate public POIs via admin CRUD interface, synced with localized feeds.
FR-IE-05: Cache frequent queries in Redis for microsecond retrieval.
3.2.3 Custom Maps and Geofencing
FR-CM-01: Users shall create custom maps with unique identifiers.
FR-CM-02: Users shall add/edit custom fields to maps (e.g., name, type, validation rules).
FR-CM-03: Users shall edit existing maps, including fields and metadata.
FR-CM-04: Users shall share maps via public/private keys or links.
FR-CM-05: Users shall perform CRUD operations on custom places (points) on maps, linked to lat/long.
FR-CM-06: Users shall perform CRUD operations on custom polygons on maps, using GeoJSON.
FR-CM-07: Users shall enable geofencing on moving items (paid feature, available in Self-Serve and above), assigning unique API keys per item.
FR-CM-08: Users shall set multiple geofencing rules for moving items (away, close, in range), triggering alerts via Twilio/Ably.
FR-CM-09: Support geofencing types: All administrative levels (province, district, sector, cell, village) and custom polygons.
FR-CM-10: Maps shall support multiple map data sets; each set can have multiple items connected to places.
FR-CM-11: Map data items shall use custom fields for population and be paginated (20 items/page).
FR-CM-12: Geofencing and map data shall integrate with Ngabo GIS Travel Engine for calculations.
3.2.4 Ngabo GIS Data Engine
FR-DE-01: Act as intermediary between Indexing and DataMap, enabling filters and searches on single maps.
Example: Query 'drug', filter by pharmacy, sort by price (low-high), select range/location; pass to Travel Engine via OSRM.
FR-DE-02: Support data analytics: Generate heatmaps (kernel density), choropleth mapping (coloring areas by data), nearest N points, "within" queries (e.g., customers in a district).
FR-DE-03: Enable custom map creation with GeoJSON (Point/Line/Polygon) ingestion via MySQL spatial columns.
FR-DE-04: Provide bulk imports (CSV/Excel) with Nominatim auto-geocoding.
FR-DE-05: Enable multi-user collaboration with CRDT-based merges and ACLs.
FR-DE-06: Store and query user-uploaded datasets (e.g., inventory, pricing) with schema-on-read.
3.2.5 Ngabo GIS Travel Engine
FR-TE-01: Perform geofencing calculations for moving items, supporting administrative and custom polygons.
FR-TE-02: Provide navigation from-to-through, connecting to Indexing Engine for nearby locations.
FR-TE-03: Solve TSP/VRP:
Input: Depot (lat/lng), waypoints with payloads (e.g., "medicine paracetamol <5000 RWF"), constraints (capacity, time windows).
Process: Indexing resolves queries to POIs via Nominatim; Data enriches with payloads; Optimize via proprietary heuristics (IP: RW/2025/002) layered on OSRM.
Output: GeoJSON LineString with metrics (ETA, cost, distance).
FR-TE-04: Support real-time asset tracking with WebSocket updates; simulation tools for testing.
FR-TE-05: Cache route matrices in Redis for microsecond replays.
TSP Workflow Example:
Query: "Find cheapest paracetamol <5000 RWF near Nyabugogo, plan delivery route."
Indexing: Resolves to 5 pharmacies via Nominatim (98% accuracy).
Data: Fuses stock/pricing from user CSVs, localized feeds.
Travel: Computes TSP tour (e.g., depot → Pharma A → Pharma B → return, 12min, 8km), visualized with turn-by-turn instructions.
3.2.6 Sectoral Modules
FR-SM-01: Health
Ingest facility and case data for outbreak choropleths.
Dispatch workflow: Incident pin → nearest ambulance (k-NN) → TSP route.
Example: "Insulin <10000 Muhanga" → Clinic tour, reducing stockouts.
FR-SM-02: Agriculture
Fuse satellite data for yield heatmaps; VRP for farm-to-market routes.
Example: "Fertilizer <15000 Musanze" → Co-op circuit, optimizing logistics.
FR-SM-03: Logistics
VRP for parcel deliveries; congestion heatmaps.
Example: "10 orders <2000 RWF" → Optimized circuit, cutting fuel use.
FR-SM-04: Government
Match service entities (e.g., birth certificate offices) via Indexing; route via TSP.
Example: "Birth certificate <5000 Gisenyi" → Service path, enhancing access.
FR-SM-05 to FR-SM-10: Similar for mining, education, tourism, real estate, fintech, utilities, mirroring health/agriculture patterns.
3.2.7 Administration and Utilities
FR-AU-01: User auth/roles via OIDC/RBAC; SAML SSO for Enterprise tier.
FR-AU-02: API dashboards for usage monitoring, segmented by pricing tier.
FR-AU-03: Data export (PDF/GPX/GeoJSON).
FR-AU-04: Billing dashboard for Pay-As-You-Go and subscription users; invoice generation for government/NGOs.
3.3 Non-Functional Requirements
3.3.1 Performance
Latency: <3s for TSP (50 stops); microsecond caching for repeated queries via Laravel Cognitive Caching (TypeScript + Laravel + Redis + RoadRunner).
Throughput: 1M qps with Dockerized scaling; 3,000 lookups/min for Self-Serve Unlimited and above.
Scalability: Horizontal pods; 99.9% uptime SLA for Enterprise tier.
3.3.2 Security
Encryption: At-rest (AES-256), in-transit (TLS 1.3).
Access: Zero-trust; audit logs for IP-sensitive operations; SAML SSO for Enterprise.
Compliance: Rwanda Data Protection Law; SOC 2 Type 2 and HIPAA/HITECH for Enterprise; no data egress for On-Premises.
3.3.3 Reliability
Availability: 99.9% with Redis failover; 99.9% SLA for Enterprise.
Fault Tolerance: Graceful degradation (e.g., cached routes during outages).
3.3.4 Maintainability
Modularity: Microservices for engines; CI/CD via GitHub Actions.
Documentation: Inline comments; OpenAPI specs.
Testing: Pest for unit and integration tests; Postman for API tests; Locust for load testing.
3.3.5 Usability
UI: Intuitive tabs; WCAG 2.1 compliant.
Accessibility: Screen-reader support; multilingual (Kinyarwanda/English).
3.3.6 Portability
Cross-Platform: Web/mobile; Dockerized for any cloud or on-premises.
3.4 Database Requirements
Schema: MySQL 8.0 with spatial columns (e.g., GEOMETRY for GeoJSON); tables for POIs (EAV model), DataMapItems, Telemetry (time-series), Billing (usage tracking).
Indexing: Spatial (SPATIAL INDEX), full-text (FULLTEXT for searches).
Backup: Automated snapshots; replication for HA.
3.5 Other Requirements
Logging: ELK stack for observability.
Testing: Pest for unit/integration tests; Postman for API tests; Locust for load testing.
Containerization: Docker Compose for services; Kubernetes for orchestration; On-Premises tier as Docker image.
4. Supporting Information
4.1 Assumptions
Users have stable internet for cloud tiers; On-Premises tier supports offline use.
Localized datasets seeded from verified sources.
4.2 Dependencies
External: Nominatim/OSRM (on-prem), Twilio, Ably, Stripe (billing).
Internal: Engines interdependent via gRPC; Redis/RoadRunner for performance.
